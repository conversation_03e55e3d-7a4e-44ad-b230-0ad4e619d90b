import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Search, FolderOpen, Lock, Globe, User, Calendar, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';
import { ProjectAccessService } from '@/lib/projectAccess';
import { Project } from '@/lib/supabase';
import { useNavigate } from 'react-router-dom';

interface AccessibleProjectsProps {
  className?: string;
}

const AccessibleProjects: React.FC<AccessibleProjectsProps> = ({ className }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    loadAccessibleProjects();
  }, []);

  useEffect(() => {
    // Filter projects based on search term
    if (searchTerm.trim()) {
      const filtered = projects.filter(project =>
        project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.theme_category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProjects(filtered);
    } else {
      setFilteredProjects(projects);
    }
  }, [projects, searchTerm]);

  const loadAccessibleProjects = async () => {
    setLoading(true);
    try {
      const { projects: accessibleProjects, error } = await ProjectAccessService.getUserAccessibleProjects({
        limit: 100,
      });

      if (error) {
        toast.error('Failed to load accessible projects');
        console.error('Error loading accessible projects:', error);
      } else {
        setProjects(accessibleProjects);
      }
    } catch (error) {
      console.error('Error loading accessible projects:', error);
      toast.error('Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const handleProjectClick = (project: Project) => {
    // Navigate to the project
    navigate(`/project/${project.id}`);
  };

  const getProjectIcon = (project: Project) => {
    if (project.is_public) {
      return <Globe className="h-4 w-4 text-green-500" />;
    }
    return <Lock className="h-4 w-4 text-blue-500" />;
  };

  const getProjectTypeLabel = (project: Project) => {
    if (project.is_public) {
      return 'Public';
    }
    return 'Shared';
  };

  const groupProjectsByCategory = (projects: Project[]) => {
    const grouped = projects.reduce((acc, project) => {
      const category = project.theme_category || 'Uncategorized';
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(project);
      return acc;
    }, {} as Record<string, Project[]>);

    return grouped;
  };

  const groupedProjects = groupProjectsByCategory(filteredProjects);

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Accessible Projects
          </CardTitle>
          <CardDescription>
            Projects you have access to, including your own, shared, and public projects
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search projects by title, description, or category..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Projects */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-muted-foreground mt-2">Loading projects...</p>
            </div>
          ) : Object.keys(groupedProjects).length === 0 ? (
            <div className="text-center py-8">
              <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No accessible projects found</p>
              <p className="text-sm text-muted-foreground">
                {searchTerm ? 'Try adjusting your search terms' : 'Create a project or ask an admin for access to themed projects'}
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedProjects).map(([category, categoryProjects]) => (
                <div key={category}>
                  <div className="flex items-center gap-2 mb-4">
                    <h3 className="text-lg font-semibold">{category}</h3>
                    <Badge variant="secondary">{categoryProjects.length}</Badge>
                  </div>
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {categoryProjects.map((project) => (
                      <Card key={project.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleProjectClick(project)}>
                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium truncate">{project.name}</h4>
                                {project.description && (
                                  <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                                    {project.description}
                                  </p>
                                )}
                              </div>
                              <div className="flex items-center gap-1 ml-2">
                                {getProjectIcon(project)}
                                <ExternalLink className="h-3 w-3 text-muted-foreground" />
                              </div>
                            </div>

                            <div className="flex items-center justify-between text-xs text-muted-foreground">
                              <div className="flex items-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                  {getProjectTypeLabel(project)}
                                </Badge>
                                {project.theme_category && project.theme_category !== category && (
                                  <Badge variant="secondary" className="text-xs">
                                    {project.theme_category}
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{new Date(project.created_at).toLocaleDateString()}</span>
                              </div>
                            </div>

                            {/* Project stats */}
                            {project.content && (
                              <div className="flex items-center gap-4 text-xs text-muted-foreground pt-2 border-t">
                                {project.content.metadata?.wordCount && (
                                  <span>{project.content.metadata.wordCount} words</span>
                                )}
                                {project.content.iterations && (
                                  <span>{project.content.iterations.length} iterations</span>
                                )}
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Summary */}
          {!loading && filteredProjects.length > 0 && (
            <div className="pt-4 border-t">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>
                  Showing {filteredProjects.length} of {projects.length} accessible projects
                </span>
                <Button variant="outline" size="sm" onClick={loadAccessibleProjects}>
                  Refresh
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AccessibleProjects;
