-- SaaS Features Migration
-- Adds subscription management, master account features, and project access control

-- Add stripe_customer_id to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;

-- Create subscription plans table
CREATE TABLE public.subscription_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10,2) NOT NULL,
    price_yearly DECIMAL(10,2),
    stripe_price_id_monthly TEXT,
    stripe_price_id_yearly TEXT,
    features JSONB DEFAULT '{}',
    limits JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user subscriptions table
CREATE TABLE public.user_subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    plan_id UUID REFERENCES public.subscription_plans(id) NOT NULL,
    stripe_customer_id TEXT,
    stripe_subscription_id TEXT,
    status TEXT NOT NULL DEFAULT 'active', -- active, canceled, past_due, unpaid
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create usage tracking table
CREATE TABLE public.user_usage (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    month_year TEXT NOT NULL, -- Format: YYYY-MM
    prompts_generated INTEGER DEFAULT 0,
    projects_created INTEGER DEFAULT 0,
    api_calls INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, month_year)
);

-- Create master project templates table
CREATE TABLE public.master_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    content JSONB NOT NULL,
    required_plan TEXT DEFAULT 'free', -- free, premium, admin
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create project access permissions table
CREATE TABLE public.project_access (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    granted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    access_type TEXT DEFAULT 'view', -- view, edit, admin
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(project_id, user_id)
);

-- Create payment history table
CREATE TABLE public.payment_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    stripe_payment_intent_id TEXT,
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'usd',
    status TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX idx_user_usage_user_month ON public.user_usage(user_id, month_year);
CREATE INDEX idx_master_templates_category ON public.master_templates(category);
CREATE INDEX idx_master_templates_required_plan ON public.master_templates(required_plan);
CREATE INDEX idx_project_access_project_user ON public.project_access(project_id, user_id);
CREATE INDEX idx_payment_history_user_id ON public.payment_history(user_id);

-- Insert default subscription plans
INSERT INTO public.subscription_plans (name, description, price_monthly, price_yearly, features, limits) VALUES
('Free', 'Basic access to prompt generation', 0.00, 0.00, 
 '{"basic_prompts": true, "save_projects": true, "community_access": true}',
 '{"monthly_prompts": 50, "max_projects": 10, "api_calls_per_day": 100}'),
('Premium', 'Unlimited prompts and premium templates', 7.00, 70.00,
 '{"unlimited_prompts": true, "premium_templates": true, "priority_support": true, "advanced_styles": true}',
 '{"monthly_prompts": -1, "max_projects": -1, "api_calls_per_day": -1}'),
('Master', 'Create and manage template collections', 19.00, 190.00,
 '{"all_premium_features": true, "create_templates": true, "user_management": true, "analytics": true}',
 '{"monthly_prompts": -1, "max_projects": -1, "api_calls_per_day": -1, "template_collections": -1}');

-- Enable RLS on new tables
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.master_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_history ENABLE ROW LEVEL SECURITY;

-- RLS Policies for subscription_plans
CREATE POLICY "Anyone can view active subscription plans" ON public.subscription_plans
    FOR SELECT USING (is_active = true);

-- RLS Policies for user_subscriptions
CREATE POLICY "Users can view their own subscriptions" ON public.user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own subscriptions" ON public.user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all subscriptions" ON public.user_subscriptions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND subscription_status = 'admin'
        )
    );

-- RLS Policies for user_usage
CREATE POLICY "Users can view their own usage" ON public.user_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own usage" ON public.user_usage
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own usage" ON public.user_usage
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for master_templates
CREATE POLICY "Users can view templates based on their plan" ON public.master_templates
    FOR SELECT USING (
        is_active = true AND (
            required_plan = 'free' OR
            (required_plan = 'premium' AND EXISTS (
                SELECT 1 FROM public.profiles 
                WHERE id = auth.uid() AND subscription_status IN ('premium', 'admin')
            )) OR
            (required_plan = 'admin' AND EXISTS (
                SELECT 1 FROM public.profiles 
                WHERE id = auth.uid() AND subscription_status = 'admin'
            ))
        )
    );

CREATE POLICY "Admins can manage all templates" ON public.master_templates
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND subscription_status = 'admin'
        )
    );

-- RLS Policies for project_access
CREATE POLICY "Users can view their granted access" ON public.project_access
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Project owners can manage access" ON public.project_access
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects 
            WHERE id = project_access.project_id AND user_id = auth.uid()
        )
    );

-- RLS Policies for payment_history
CREATE POLICY "Users can view their own payment history" ON public.payment_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own payment history" ON public.payment_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create triggers for updated_at
CREATE TRIGGER update_subscription_plans_updated_at
    BEFORE UPDATE ON public.subscription_plans
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON public.user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_usage_updated_at
    BEFORE UPDATE ON public.user_usage
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_master_templates_updated_at
    BEFORE UPDATE ON public.master_templates
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
