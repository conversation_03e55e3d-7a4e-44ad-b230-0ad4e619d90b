import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useProject } from '@/hooks/useProjects';
import { projectUtils } from '@/lib/projects';
import { ProjectAccessService } from '@/lib/projectAccess';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import BackgroundEffects from '@/components/BackgroundEffects';
import Logo from '@/components/Logo';
import PromptOutput from '@/components/PromptOutput';
import { 
  ArrowL<PERSON>t, 
  Edit, 
  Trash2, 
  Heart, 
  HeartOff, 
  Play, 
  Calendar,
  User,
  Eye,
  Globe,
  Lock,
  History,
  Copy,
  Download
} from 'lucide-react';
import { toast } from 'sonner';
import { motion } from 'framer-motion';

const ProjectView: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [selectedIteration, setSelectedIteration] = useState<number>(0);
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [accessType, setAccessType] = useState<string>('');
  const [accessLoading, setAccessLoading] = useState<boolean>(true);

  const {
    project,
    loading,
    error,
    deleteProject,
    toggleFavorite
  } = useProject(projectId!);

  // Check project access
  useEffect(() => {
    const checkAccess = async () => {
      if (!projectId || !user) {
        setAccessLoading(false);
        return;
      }

      try {
        const { hasAccess: access, accessType: type, error: accessError } = await ProjectAccessService.hasProjectAccess(projectId);

        if (accessError) {
          console.error('Error checking project access:', accessError);
          setHasAccess(false);
        } else {
          setHasAccess(access);
          setAccessType(type || '');
        }
      } catch (error) {
        console.error('Error checking project access:', error);
        setHasAccess(false);
      } finally {
        setAccessLoading(false);
      }
    };

    checkAccess();
  }, [projectId, user]);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this project?')) {
      const success = await deleteProject();
      if (success) {
        navigate('/projects');
      }
    }
  };

  const handleToggleFavorite = async () => {
    await toggleFavorite();
  };

  const handleContinueWorking = () => {
    if (!project) return;

    const projectContent = (project as any).content;
    const hasLegacyContent = projectContent && projectContent.userIdea;

    if (hasLegacyContent) {
      // Navigate to main page with project data loaded (legacy projects)
      const searchParams = new URLSearchParams({
        projectId: project.id,
        userIdea: projectContent.userIdea,
        selectedStyle: projectContent.selectedStyle,
        loadProject: 'true'
      });

      navigate(`/?${searchParams.toString()}`);
    } else {
      // For new schema projects, just navigate to home
      navigate('/');
    }
  };

  const handleCopyPrompt = async (prompt: string) => {
    await navigator.clipboard.writeText(prompt);
    toast.success('Prompt copied to clipboard');
  };

  // Show loading while checking access or loading project
  if (loading || accessLoading) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <BackgroundEffects />
        <div className="relative z-10">
          <header className="flex justify-between items-center p-6">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => navigate('/projects')}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Projects
              </Button>
              <Logo />
            </div>
          </header>
          
          <main className="container max-w-4xl mx-auto px-6">
            <div className="space-y-6">
              <Skeleton className="h-8 w-1/2" />
              <Skeleton className="h-4 w-1/3" />
              <Card>
                <CardHeader>
                  <Skeleton className="h-6 w-1/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-32 w-full" />
                </CardContent>
              </Card>
            </div>
          </main>
        </div>
      </div>
    );
  }

  // Check access permissions
  if (!accessLoading && !hasAccess) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <BackgroundEffects />
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <Card className="w-full max-w-md mx-4">
            <CardHeader className="text-center">
              <CardTitle className="flex items-center justify-center gap-2">
                <Lock className="h-5 w-5" />
                Access Denied
              </CardTitle>
              <CardDescription>
                You don't have permission to view this project. Contact the project owner or an admin for access.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <Button onClick={() => navigate('/member')} variant="default">
                View My Projects
              </Button>
              <Button onClick={() => navigate('/projects')} variant="outline">
                Back to Projects
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <BackgroundEffects />
        <div className="relative z-10 flex items-center justify-center min-h-screen">
          <Card className="w-full max-w-md mx-4">
            <CardHeader className="text-center">
              <CardTitle>Project Not Found</CardTitle>
              <CardDescription>
                The project you're looking for doesn't exist or you don't have access to it.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button onClick={() => navigate('/projects')}>
                Back to Projects
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const stats = projectUtils.getProjectStats(project);
  const formattedDate = projectUtils.formatDate(project.updated_at);
  const createdDate = projectUtils.formatDate(project.created_at);

  // Handle both old and new schema
  const projectContent = (project as any).content;
  const hasLegacyContent = projectContent && projectContent.userIdea;

  // Get all iterations including the current one
  const allIterations = hasLegacyContent ? [
    {
      id: 'current',
      userIdea: projectContent.userIdea,
      selectedStyle: projectContent.selectedStyle,
      generatedPrompt: projectContent.generatedPrompt,
      timestamp: project.updated_at
    },
    ...(projectContent.iterations || [])
  ].reverse() : []; // Show newest first

  const currentIteration = allIterations[selectedIteration];

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundEffects />
      
      <div className="relative z-10">
        {/* Header */}
        <header className="flex justify-between items-center p-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/projects')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Projects
            </Button>
            <Logo />
          </div>

          <div className="flex items-center gap-2">
            {/* Access type indicator */}
            {accessType && accessType !== 'admin' && (
              <Badge variant={accessType === 'edit' ? 'default' : 'secondary'} className="mr-2">
                {accessType === 'view' ? (
                  <>
                    <Eye className="w-3 h-3 mr-1" />
                    View Only
                  </>
                ) : accessType === 'edit' ? (
                  <>
                    <Edit className="w-3 h-3 mr-1" />
                    Edit Access
                  </>
                ) : null}
              </Badge>
            )}

            {/* Only show favorite/edit actions if user has edit access or owns the project */}
            {(accessType === 'admin' || accessType === 'edit') && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleFavorite}
                className="flex items-center gap-2"
              >
                {stats.isFavorite ? (
                  <>
                    <HeartOff className="w-4 h-4" />
                    Remove Favorite
                  </>
                ) : (
                  <>
                    <Heart className="w-4 h-4" />
                    Add Favorite
                  </>
                )}
              </Button>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={handleContinueWorking}
              className="flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              Continue Working
            </Button>

            {/* Only show delete if user owns the project (admin access) */}
            {accessType === 'admin' && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleDelete}
                className="flex items-center gap-2 text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
                Delete
              </Button>
            )}
          </div>
        </header>

        {/* Main Content */}
        <main className="container max-w-6xl mx-auto px-6 pb-16">
          {/* Project Header */}
          <div className="mb-8">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h1 className="text-4xl font-bold tracking-tight mb-2">
                  {(project as any).title || project.name}
                </h1>
                {project.description && (
                  <p className="text-xl text-gray-600 mb-4">
                    {project.description}
                  </p>
                )}
              </div>
            </div>

            {/* Metadata */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>Updated {formattedDate}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <History className="w-4 h-4" />
                <span>{stats.iterationCount + 1} iterations</span>
              </div>
              
              <div className="flex items-center gap-2">
                {project.is_public ? (
                  <>
                    <Globe className="w-4 h-4" />
                    <span>Public</span>
                  </>
                ) : (
                  <>
                    <Lock className="w-4 h-4" />
                    <span>Private</span>
                  </>
                )}
              </div>

              {(project as any).theme_category && (
                <Badge variant="secondary">
                  {(project as any).theme_category}
                </Badge>
              )}

              {stats.isFavorite && (
                <Badge variant="outline" className="text-red-600 border-red-200">
                  <Heart className="w-3 h-3 mr-1 fill-current" />
                  Favorite
                </Badge>
              )}
            </div>
          </div>

          {/* Content Tabs */}
          <Tabs defaultValue="current" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="current">Current Version</TabsTrigger>
              <TabsTrigger value="history">
                History ({allIterations.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="current" className="space-y-6">
              {hasLegacyContent ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Edit className="w-5 h-5" />
                      Current Prompt
                    </CardTitle>
                    <CardDescription>
                      Style: {projectContent.selectedStyle} • {stats.wordCount} words
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Original Idea:</h4>
                        <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                          {projectContent.userIdea}
                        </p>
                      </div>

                      <Separator />

                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">Generated Prompt:</h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCopyPrompt(projectContent.generatedPrompt)}
                            className="flex items-center gap-2"
                          >
                            <Copy className="w-4 h-4" />
                            Copy
                          </Button>
                        </div>
                        <PromptOutput
                          content={projectContent.generatedPrompt}
                          userIdea={projectContent.userIdea}
                          selectedStyle={projectContent.selectedStyle}
                          currentProjectId={project.id}
                          onProjectSaved={() => {}}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Edit className="w-5 h-5" />
                      Project Details
                    </CardTitle>
                    <CardDescription>
                      This project uses the new format
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2">Description:</h4>
                        <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                          {project.description || 'No description provided'}
                        </p>
                      </div>

                      {project.custom_instructions && (
                        <>
                          <Separator />
                          <div>
                            <h4 className="font-medium mb-2">Custom Instructions:</h4>
                            <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                              {project.custom_instructions}
                            </p>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              {allIterations.length > 1 ? (
                <div className="space-y-4">
                  {allIterations.map((iteration, index) => (
                    <motion.div
                      key={iteration.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card className={index === 0 ? 'border-blue-200 bg-blue-50/30' : ''}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-lg">
                              {index === 0 ? 'Current Version' : `Iteration ${allIterations.length - index}`}
                            </CardTitle>
                            <div className="flex items-center gap-2">
                              <span className="text-sm text-gray-500">
                                {projectUtils.formatDate(iteration.timestamp)}
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleCopyPrompt(iteration.generatedPrompt)}
                              >
                                <Copy className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                          <CardDescription>
                            Style: {iteration.selectedStyle}
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div>
                              <h5 className="font-medium text-sm mb-1">Idea:</h5>
                              <p className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                                {iteration.userIdea}
                              </p>
                            </div>
                            <div>
                              <h5 className="font-medium text-sm mb-1">Generated Prompt:</h5>
                              <div className="text-sm bg-white border rounded-lg p-3 max-h-40 overflow-y-auto">
                                {iteration.generatedPrompt}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              ) : (
                <Card className="text-center py-12">
                  <CardContent>
                    <History className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">No Previous Iterations</h3>
                    <p className="text-gray-600 mb-6">
                      This project only has the current version. Continue working to create iterations.
                    </p>
                    <Button onClick={handleContinueWorking} className="flex items-center gap-2">
                      <Play className="w-4 h-4" />
                      Continue Working
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default ProjectView;
