import { getGroqConfig } from './groq';

export async function generateWithGroq(idea: string, style: string): Promise<string> {
  const { apiKey, model } = getGroqConfig();
  
  if (!apiKey) {
    throw new Error('GROQ API key not configured');
  }

  const systemPrompt = `You are a professional prompt engineer. Generate a ${style} style prompt based on the following idea. Keep the output format consistent with markdown.`;
  
  const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: idea }
      ],
      temperature: 1,
      max_completion_tokens: 1024,
      top_p: 1,
      stream: false, // Changed to false since we're not handling streams yet
      stop: null
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Failed to generate prompt with GROQ: ${errorData.error?.message || response.statusText}`);
  }

  const data = await response.json();
  return data.choices[0].message.content;
}
