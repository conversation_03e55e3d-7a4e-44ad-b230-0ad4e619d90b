import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import BackgroundEffects from '@/components/BackgroundEffects';
import Logo from '@/components/Logo';
import Footer from '@/components/Footer';

const Documentation = () => {
  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundEffects />
      
      <div className="relative z-10">
        <header className="flex justify-between items-center p-6">
          <Logo />
          <Link
            to="/"
            className="flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-white/90 hover:shadow-md transition-all duration-300"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to App
          </Link>
        </header>

        <main className="container max-w-4xl mx-auto px-6 py-12">
          <h1 className="text-4xl font-bold mb-8">
            How to Use <span className="text-highlight">ChatsPrompt</span>
          </h1>

          <div className="space-y-12">
            {/* Getting Started */}
            <section className="bg-white/90 backdrop-blur-sm rounded-2xl border border-blue-100 shadow-lg p-8">
              <h2 className="text-2xl font-semibold mb-4">Getting Started</h2>
              <p className="text-gray-600 mb-6">
                ChatsPrompt helps you transform simple ideas into powerful, context-rich AI prompts. Follow these steps to get the most out of our tool:
              </p>
              <ol className="space-y-4">
                <li className="flex gap-4">
                  <span className="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-semibold">1</span>
                  <div>
                    <h3 className="font-semibold mb-1">Choose Your Style</h3>
                    <p className="text-gray-600">Select from different prompt styles: Technical, Creative, Concise, or Detailed. Each style is optimized for different use cases.</p>
                  </div>
                </li>
                <li className="flex gap-4">
                  <span className="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-semibold">2</span>
                  <div>
                    <h3 className="font-semibold mb-1">Enter Your Idea</h3>
                    <p className="text-gray-600">Type your basic idea or concept into the input field. Be specific but concise.</p>
                  </div>
                </li>
                <li className="flex gap-4">
                  <span className="flex-shrink-0 w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-semibold">3</span>
                  <div>
                    <h3 className="font-semibold mb-1">Generate & Customize</h3>
                    <p className="text-gray-600">Click generate and get your AI-optimized prompt. You can copy, download, or modify the result.</p>
                  </div>
                </li>
              </ol>
            </section>

            {/* Prompt Styles */}
            <section className="bg-white/90 backdrop-blur-sm rounded-2xl border border-blue-100 shadow-lg p-8">
              <h2 className="text-2xl font-semibold mb-4">Prompt Styles Explained</h2>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="p-4 border border-blue-100 rounded-xl">
                  <h3 className="font-semibold text-blue-600 mb-2">Technical</h3>
                  <p className="text-gray-600">Structured prompts with technical specifications. Perfect for development projects and technical documentation.</p>
                </div>
                <div className="p-4 border border-blue-100 rounded-xl">
                  <h3 className="font-semibold text-blue-600 mb-2">Creative</h3>
                  <p className="text-gray-600">Open-ended prompts that encourage imaginative responses. Ideal for brainstorming and creative projects.</p>
                </div>
                <div className="p-4 border border-blue-100 rounded-xl">
                  <h3 className="font-semibold text-blue-600 mb-2">Concise</h3>
                  <p className="text-gray-600">Brief, focused prompts that get straight to the point. Best for quick iterations and clear instructions.</p>
                </div>
                <div className="p-4 border border-blue-100 rounded-xl">
                  <h3 className="font-semibold text-blue-600 mb-2">Detailed</h3>
                  <p className="text-gray-600">Comprehensive prompts with extensive context. Perfect for complex projects requiring specific guidance.</p>
                </div>
              </div>
            </section>

            {/* Tips & Best Practices */}
            <section className="bg-white/90 backdrop-blur-sm rounded-2xl border border-blue-100 shadow-lg p-8">
              <h2 className="text-2xl font-semibold mb-4">Tips & Best Practices</h2>
              <ul className="space-y-4">
                <li className="flex items-start gap-3">
                  <span className="mt-1.5 w-2 h-2 rounded-full bg-blue-400 flex-shrink-0" />
                  <p className="text-gray-600">Be specific with your initial idea - the more focused your input, the better the generated prompt.</p>
                </li>
                <li className="flex items-start gap-3">
                  <span className="mt-1.5 w-2 h-2 rounded-full bg-blue-400 flex-shrink-0" />
                  <p className="text-gray-600">Experiment with different styles to find what works best for your use case.</p>
                </li>
                <li className="flex items-start gap-3">
                  <span className="mt-1.5 w-2 h-2 rounded-full bg-blue-400 flex-shrink-0" />
                  <p className="text-gray-600">Use the download feature to save prompts for future reference or sharing.</p>
                </li>
                <li className="flex items-start gap-3">
                  <span className="mt-1.5 w-2 h-2 rounded-full bg-blue-400 flex-shrink-0" />
                  <p className="text-gray-600">Review and customize generated prompts to better match your specific needs.</p>
                </li>
              </ul>
            </section>
          </div>
        </main>

        <Footer />
      </div>
    </div>
  );
};

export default Documentation;

