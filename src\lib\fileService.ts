import { supabase, ProjectFile, ProjectFileInsert } from './supabase';

export interface UploadFileData {
  file: File;
  projectId: string;
}

export interface FileUploadResult {
  file: ProjectFile | null;
  error: Error | null;
}

export class FileService {
  private static readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private static readonly ALLOWED_TYPES = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/msword', // .doc
    'text/plain',
    'text/markdown',
    'text/csv',
    'application/vnd.ms-excel', // .xls
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/json',
    'text/html',
    'application/rtf'
  ];

  /**
   * Upload a file to a project
   */
  static async uploadFile(data: UploadFileData): Promise<FileUploadResult> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { file: null, error: new Error('User not authenticated') };
      }

      // Validate file
      const validation = this.validateFile(data.file);
      if (!validation.isValid) {
        return { file: null, error: new Error(validation.error!) };
      }

      // Generate unique filename
      const fileExtension = data.file.name.split('.').pop();
      const uniqueFilename = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`;
      const storagePath = `projects/${data.projectId}/${uniqueFilename}`;

      // Upload file to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('project-files')
        .upload(storagePath, data.file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('Error uploading file to storage:', uploadError);
        return { file: null, error: new Error('Failed to upload file') };
      }

      // Extract content from file
      const extractedContent = await this.extractFileContent(data.file);

      // Save file metadata to database
      const fileData: ProjectFileInsert = {
        project_id: data.projectId,
        user_id: user.id,
        filename: uniqueFilename,
        original_filename: data.file.name,
        file_type: data.file.type,
        file_size: data.file.size,
        storage_path: storagePath,
        extracted_content: extractedContent,
        metadata: {
          uploadedAt: new Date().toISOString(),
          extractedContentLength: extractedContent?.length || 0
        }
      };

      const { data: fileRecord, error: dbError } = await supabase
        .from('project_files')
        .insert(fileData)
        .select()
        .single();

      if (dbError) {
        // Clean up uploaded file if database insert fails
        await supabase.storage.from('project-files').remove([storagePath]);
        console.error('Error saving file metadata:', dbError);
        return { file: null, error: new Error('Failed to save file metadata') };
      }

      return { file: fileRecord, error: null };
    } catch (error) {
      console.error('Error uploading file:', error);
      return { file: null, error: error as Error };
    }
  }

  /**
   * Get files for a project
   */
  static async getProjectFiles(projectId: string): Promise<{ files: ProjectFile[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { files: [], error: new Error('User not authenticated') };
      }

      const { data: files, error } = await supabase
        .from('project_files')
        .select('*')
        .eq('project_id', projectId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching project files:', error);
        return { files: [], error: new Error(error.message) };
      }

      return { files: files || [], error: null };
    } catch (error) {
      console.error('Error fetching project files:', error);
      return { files: [], error: error as Error };
    }
  }

  /**
   * Delete a file
   */
  static async deleteFile(fileId: string): Promise<{ error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { error: new Error('User not authenticated') };
      }

      // Get file info first
      const { data: file, error: fetchError } = await supabase
        .from('project_files')
        .select('storage_path')
        .eq('id', fileId)
        .eq('user_id', user.id)
        .single();

      if (fetchError || !file) {
        return { error: new Error('File not found') };
      }

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('project-files')
        .remove([file.storage_path]);

      if (storageError) {
        console.error('Error deleting file from storage:', storageError);
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('project_files')
        .delete()
        .eq('id', fileId)
        .eq('user_id', user.id);

      if (dbError) {
        console.error('Error deleting file from database:', dbError);
        return { error: new Error(dbError.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Error deleting file:', error);
      return { error: error as Error };
    }
  }

  /**
   * Get download URL for a file
   */
  static async getFileUrl(storagePath: string): Promise<{ url: string | null; error: Error | null }> {
    try {
      const { data, error } = await supabase.storage
        .from('project-files')
        .createSignedUrl(storagePath, 3600); // 1 hour expiry

      if (error) {
        console.error('Error creating signed URL:', error);
        return { url: null, error: new Error(error.message) };
      }

      return { url: data.signedUrl, error: null };
    } catch (error) {
      console.error('Error getting file URL:', error);
      return { url: null, error: error as Error };
    }
  }

  /**
   * Validate file before upload
   */
  private static validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        isValid: false,
        error: `File size must be less than ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
      };
    }

    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return {
        isValid: false,
        error: 'File type not supported. Please upload PDF, Word documents, images, or text files.'
      };
    }

    return { isValid: true };
  }

  /**
   * Extract text content from files
   */
  private static async extractFileContent(file: File): Promise<string | null> {
    try {
      // For text files, read directly
      if (file.type.startsWith('text/') || file.type === 'application/json') {
        return await file.text();
      }

      // For images, return basic metadata
      if (file.type.startsWith('image/')) {
        return `Image file: ${file.name} (${file.type}, ${(file.size / 1024).toFixed(1)}KB)`;
      }

      // For other file types, we would need additional libraries
      // For now, return basic file info
      return `File: ${file.name} (${file.type}, ${(file.size / 1024).toFixed(1)}KB)`;
    } catch (error) {
      console.error('Error extracting file content:', error);
      return null;
    }
  }

  /**
   * Get file type icon
   */
  static getFileTypeIcon(fileType: string): string {
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word') || fileType.includes('document')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('text')) return '📃';
    if (fileType.includes('json')) return '🔧';
    return '📎';
  }

  /**
   * Format file size
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
