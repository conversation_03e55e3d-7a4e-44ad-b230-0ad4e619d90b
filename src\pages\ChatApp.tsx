import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import ChatSidebar from '@/components/ChatSidebar';
import ChatInterface from '@/components/ChatInterface';
import { ChatService } from '@/lib/chatService';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

const ChatApp: React.FC = () => {
  const { chatId, projectId } = useParams<{ chatId?: string; projectId?: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | undefined>(chatId);
  const [currentProjectId, setCurrentProjectId] = useState<string | undefined>(projectId);
  const [showNewProjectDialog, setShowNewProjectDialog] = useState(false);
  const [sidebarKey, setSidebarKey] = useState(0); // Force sidebar refresh
  const [newProjectForm, setNewProjectForm] = useState({
    name: '',
    description: '',
    customInstructions: ''
  });
  const [creatingProject, setCreatingProject] = useState(false);

  useEffect(() => {
    setCurrentChatId(chatId);
    setCurrentProjectId(projectId);
  }, [chatId, projectId]);

  // Redirect to auth if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      navigate('/auth');
    }
  }, [isAuthenticated, authLoading, navigate]);

  const handleChatSelect = (selectedChatId: string) => {
    setCurrentChatId(selectedChatId);
    navigate(`/chat/${selectedChatId}`);
  };

  const handleNewChat = () => {
    setCurrentChatId(undefined);
    setCurrentProjectId(undefined);
    navigate('/chat');
  };

  const handleNewProject = () => {
    setShowNewProjectDialog(true);
  };

  const handleChatCreated = (newChatId: string) => {
    setCurrentChatId(newChatId);
    navigate(`/chat/${newChatId}`);
    setSidebarKey(prev => prev + 1); // Refresh sidebar
  };

  const createProject = async () => {
    if (!newProjectForm.name.trim()) {
      toast.error('Project name is required');
      return;
    }

    setCreatingProject(true);

    try {
      const { project, error } = await ChatService.createProject({
        name: newProjectForm.name.trim(),
        description: newProjectForm.description.trim() || undefined,
        customInstructions: newProjectForm.customInstructions.trim() || undefined
      });

      if (error || !project) {
        toast.error('Failed to create project');
        return;
      }

      toast.success('Project created successfully');
      setShowNewProjectDialog(false);
      setNewProjectForm({ name: '', description: '', customInstructions: '' });

      // Navigate to the new project
      setCurrentProjectId(project.id);
      setCurrentChatId(undefined);
      navigate(`/project/${project.id}`);
      setSidebarKey(prev => prev + 1); // Refresh sidebar

    } catch (error) {
      console.error('Error creating project:', error);
      toast.error('Failed to create project');
    } finally {
      setCreatingProject(false);
    }
  };

  const handleCancelNewProject = () => {
    setShowNewProjectDialog(false);
    setNewProjectForm({ name: '', description: '', customInstructions: '' });
  };

  if (authLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect to auth
  }

  return (
    <div className="h-screen bg-white">
      {/* Sidebar */}
      <ChatSidebar
        key={sidebarKey}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        currentChatId={currentChatId}
        onChatSelect={handleChatSelect}
        onNewChat={handleNewChat}
        onNewProject={handleNewProject}
      />

      {/* Main Chat Area */}
      <div className={`h-screen flex flex-col transition-all duration-200 ease-linear ${
        sidebarCollapsed ? 'ml-12' : 'ml-64'
      }`}>
        <ChatInterface
          chatId={currentChatId}
          projectId={currentProjectId}
          onChatCreated={handleChatCreated}
        />
      </div>

      {/* New Project Dialog */}
      <Dialog open={showNewProjectDialog} onOpenChange={setShowNewProjectDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create New Project</DialogTitle>
            <DialogDescription>
              Projects help you organize related conversations and maintain context across multiple chats.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="project-name">Project Name *</Label>
              <Input
                id="project-name"
                value={newProjectForm.name}
                onChange={(e) => setNewProjectForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Marketing Campaign, Technical Documentation"
                maxLength={100}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="project-description">Description</Label>
              <Textarea
                id="project-description"
                value={newProjectForm.description}
                onChange={(e) => setNewProjectForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description of your project..."
                rows={3}
                maxLength={500}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="project-instructions">Custom Instructions</Label>
              <Textarea
                id="project-instructions"
                value={newProjectForm.customInstructions}
                onChange={(e) => setNewProjectForm(prev => ({ ...prev, customInstructions: e.target.value }))}
                placeholder="e.g., Act as a marketing expert, Use formal tone, Focus on technical accuracy..."
                rows={4}
                maxLength={1000}
              />
              <p className="text-xs text-gray-500">
                These instructions will be applied to all chats in this project
              </p>
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancelNewProject} disabled={creatingProject}>
              Cancel
            </Button>
            <Button onClick={createProject} disabled={creatingProject}>
              {creatingProject && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Create Project
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ChatApp;
