import { useState, useEffect, useCallback } from 'react';
import { StripeService, SubscriptionWithPlan } from '@/lib/stripe';
import { UsageService, CurrentUsage, UsageLimits } from '@/lib/usage';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export interface UseSubscriptionReturn {
  subscription: SubscriptionWithPlan | null;
  usage: CurrentUsage | null;
  limits: UsageLimits | null;
  loading: boolean;
  error: Error | null;
  canPerformAction: (action: 'prompt' | 'project' | 'api_call') => Promise<boolean>;
  incrementUsage: (action: 'prompt' | 'project' | 'api_call', amount?: number) => Promise<boolean>;
  refreshData: () => Promise<void>;
  isUnlimited: (feature: 'prompts' | 'projects' | 'api_calls') => boolean;
  getUsagePercentage: (feature: 'prompts' | 'projects' | 'api_calls') => number;
}

export const useSubscription = (): UseSubscriptionReturn => {
  const { isAuthenticated } = useAuth();
  const [subscription, setSubscription] = useState<SubscriptionWithPlan | null>(null);
  const [usage, setUsage] = useState<CurrentUsage | null>(null);
  const [limits, setLimits] = useState<UsageLimits | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const loadData = useCallback(async () => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const [subscriptionResult, usageResult, limitsResult] = await Promise.all([
        StripeService.getUserSubscription(),
        UsageService.getCurrentUsage(),
        UsageService.getUserLimits(),
      ]);

      if (subscriptionResult.error) {
        console.error('Subscription error:', subscriptionResult.error);
      } else {
        setSubscription(subscriptionResult.subscription);
      }

      if (usageResult.error) {
        console.error('Usage error:', usageResult.error);
        setError(usageResult.error);
      } else {
        setUsage(usageResult.usage);
      }

      if (limitsResult.error) {
        console.error('Limits error:', limitsResult.error);
        setError(limitsResult.error);
      } else {
        setLimits(limitsResult.limits);
      }
    } catch (err) {
      const error = err as Error;
      console.error('Error loading subscription data:', error);
      setError(error);
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const canPerformAction = useCallback(async (action: 'prompt' | 'project' | 'api_call'): Promise<boolean> => {
    if (!isAuthenticated) {
      toast.error('Please log in to continue');
      return false;
    }

    try {
      const { allowed, error } = await UsageService.canPerformAction(action);
      
      if (error) {
        console.error('Error checking action permission:', error);
        return false;
      }

      if (!allowed) {
        const actionNames = {
          prompt: 'generate prompts',
          project: 'create projects',
          api_call: 'make API calls'
        };

        toast.error(`You've reached your limit for ${actionNames[action]}. Upgrade to continue.`, {
          action: {
            label: 'Upgrade',
            onClick: () => window.location.href = '/pricing'
          }
        });
      }

      return allowed;
    } catch (error) {
      console.error('Error checking action permission:', error);
      return false;
    }
  }, [isAuthenticated]);

  const incrementUsage = useCallback(async (action: 'prompt' | 'project' | 'api_call', amount: number = 1): Promise<boolean> => {
    if (!isAuthenticated) {
      return false;
    }

    try {
      const { success, error } = await UsageService.incrementUsage(action, amount);
      
      if (error) {
        console.error('Error incrementing usage:', error);
        return false;
      }

      if (success) {
        // Refresh usage data
        const { usage: newUsage } = await UsageService.getCurrentUsage();
        if (newUsage) {
          setUsage(newUsage);
        }
      }

      return success;
    } catch (error) {
      console.error('Error incrementing usage:', error);
      return false;
    }
  }, [isAuthenticated]);

  const refreshData = useCallback(async () => {
    await loadData();
  }, [loadData]);

  const isUnlimited = useCallback((feature: 'prompts' | 'projects' | 'api_calls'): boolean => {
    if (!limits) return false;

    switch (feature) {
      case 'prompts':
        return limits.monthly_prompts === -1;
      case 'projects':
        return limits.max_projects === -1;
      case 'api_calls':
        return limits.api_calls_per_day === -1;
      default:
        return false;
    }
  }, [limits]);

  const getUsagePercentage = useCallback((feature: 'prompts' | 'projects' | 'api_calls'): number => {
    if (!usage || !limits) return 0;

    let used = 0;
    let limit = 0;

    switch (feature) {
      case 'prompts':
        used = usage.prompts_generated;
        limit = limits.monthly_prompts;
        break;
      case 'projects':
        used = usage.projects_created;
        limit = limits.max_projects;
        break;
      case 'api_calls':
        used = usage.api_calls;
        limit = limits.api_calls_per_day * 30; // Convert daily to monthly for display
        break;
    }

    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  }, [usage, limits]);

  return {
    subscription,
    usage,
    limits,
    loading,
    error,
    canPerformAction,
    incrementUsage,
    refreshData,
    isUnlimited,
    getUsagePercentage,
  };
};

// Hook for checking if user has access to premium features
export const usePremiumFeatures = () => {
  const { subscription, loading } = useSubscription();
  const { profile } = useAuth();

  const hasPremiumAccess = !loading && (
    profile?.subscription_status === 'premium' ||
    profile?.subscription_status === 'admin' ||
    (subscription && subscription.status === 'active')
  );

  const hasAdminAccess = !loading && profile?.subscription_status === 'admin';

  const canAccessMasterTemplates = hasPremiumAccess;
  const canCreateMasterTemplates = hasAdminAccess;
  const hasUnlimitedUsage = hasPremiumAccess;

  return {
    hasPremiumAccess,
    hasAdminAccess,
    canAccessMasterTemplates,
    canCreateMasterTemplates,
    hasUnlimitedUsage,
    loading,
  };
};
