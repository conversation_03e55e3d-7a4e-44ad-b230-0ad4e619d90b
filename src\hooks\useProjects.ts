import { useState, useEffect, useCallback } from 'react';
import { ProjectsService, ProjectFilters, CreateProjectData } from '@/lib/projects';
import { Project } from '@/lib/supabase';
import { toast } from 'sonner';

export interface UseProjectsReturn {
  projects: Project[];
  loading: boolean;
  error: Error | null;
  createProject: (data: CreateProjectData) => Promise<Project | null>;
  updateProject: (projectId: string, updates: Partial<Project>) => Promise<Project | null>;
  deleteProject: (projectId: string) => Promise<boolean>;
  toggleFavorite: (projectId: string) => Promise<Project | null>;
  addIteration: (projectId: string, userIdea: string, selectedStyle: string, generatedPrompt: string) => Promise<Project | null>;
  refreshProjects: () => Promise<void>;
  setFilters: (filters: ProjectFilters) => void;
  filters: ProjectFilters;
}

export const useProjects = (initialFilters: ProjectFilters = {}): UseProjectsReturn => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [filters, setFilters] = useState<ProjectFilters>(initialFilters);

  const fetchProjects = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const { projects: fetchedProjects, error: fetchError } = await ProjectsService.getUserProjects(filters);
      
      if (fetchError) {
        setError(fetchError);
        toast.error('Failed to load projects');
      } else {
        setProjects(fetchedProjects);
      }
    } catch (err) {
      const error = err as Error;
      setError(error);
      toast.error('Failed to load projects');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  const createProject = useCallback(async (data: CreateProjectData): Promise<Project | null> => {
    try {
      const { project, error } = await ProjectsService.createProject(data);
      
      if (error) {
        toast.error('Failed to create project');
        return null;
      }

      if (project) {
        setProjects(prev => [project, ...prev]);
        toast.success('Project created successfully');
      }

      return project;
    } catch (err) {
      toast.error('Failed to create project');
      return null;
    }
  }, []);

  const updateProject = useCallback(async (projectId: string, updates: Partial<Project>): Promise<Project | null> => {
    try {
      const { project, error } = await ProjectsService.updateProject(projectId, updates);
      
      if (error) {
        toast.error('Failed to update project');
        return null;
      }

      if (project) {
        setProjects(prev => prev.map(p => p.id === projectId ? project : p));
        toast.success('Project updated successfully');
      }

      return project;
    } catch (err) {
      toast.error('Failed to update project');
      return null;
    }
  }, []);

  const deleteProject = useCallback(async (projectId: string): Promise<boolean> => {
    try {
      const { error } = await ProjectsService.deleteProject(projectId);
      
      if (error) {
        toast.error('Failed to delete project');
        return false;
      }

      setProjects(prev => prev.filter(p => p.id !== projectId));
      toast.success('Project deleted successfully');
      return true;
    } catch (err) {
      toast.error('Failed to delete project');
      return false;
    }
  }, []);

  const toggleFavorite = useCallback(async (projectId: string): Promise<Project | null> => {
    try {
      const { project, error } = await ProjectsService.toggleProjectFavorite(projectId);
      
      if (error) {
        toast.error('Failed to update favorite status');
        return null;
      }

      if (project) {
        setProjects(prev => prev.map(p => p.id === projectId ? project : p));
        const isFavorite = project.content.metadata?.isFavorite;
        toast.success(isFavorite ? 'Added to favorites' : 'Removed from favorites');
      }

      return project;
    } catch (err) {
      toast.error('Failed to update favorite status');
      return null;
    }
  }, []);

  const addIteration = useCallback(async (
    projectId: string, 
    userIdea: string, 
    selectedStyle: string, 
    generatedPrompt: string
  ): Promise<Project | null> => {
    try {
      const { project, error } = await ProjectsService.addProjectIteration(
        projectId, 
        userIdea, 
        selectedStyle, 
        generatedPrompt
      );
      
      if (error) {
        toast.error('Failed to add iteration');
        return null;
      }

      if (project) {
        setProjects(prev => prev.map(p => p.id === projectId ? project : p));
        toast.success('Iteration added successfully');
      }

      return project;
    } catch (err) {
      toast.error('Failed to add iteration');
      return null;
    }
  }, []);

  const refreshProjects = useCallback(async () => {
    await fetchProjects();
  }, [fetchProjects]);

  return {
    projects,
    loading,
    error,
    createProject,
    updateProject,
    deleteProject,
    toggleFavorite,
    addIteration,
    refreshProjects,
    setFilters,
    filters
  };
};

export interface UseProjectReturn {
  project: Project | null;
  loading: boolean;
  error: Error | null;
  updateProject: (updates: Partial<Project>) => Promise<Project | null>;
  deleteProject: () => Promise<boolean>;
  toggleFavorite: () => Promise<Project | null>;
  addIteration: (userIdea: string, selectedStyle: string, generatedPrompt: string) => Promise<Project | null>;
  refreshProject: () => Promise<void>;
}

export const useProject = (projectId: string): UseProjectReturn => {
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchProject = useCallback(async () => {
    if (!projectId) return;

    setLoading(true);
    setError(null);

    try {
      const { project: fetchedProject, error: fetchError } = await ProjectsService.getProject(projectId);
      
      if (fetchError) {
        setError(fetchError);
        toast.error('Failed to load project');
      } else {
        setProject(fetchedProject);
      }
    } catch (err) {
      const error = err as Error;
      setError(error);
      toast.error('Failed to load project');
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchProject();
  }, [fetchProject]);

  const updateProject = useCallback(async (updates: Partial<Project>): Promise<Project | null> => {
    if (!projectId) return null;

    try {
      const { project: updatedProject, error } = await ProjectsService.updateProject(projectId, updates);
      
      if (error) {
        toast.error('Failed to update project');
        return null;
      }

      if (updatedProject) {
        setProject(updatedProject);
        toast.success('Project updated successfully');
      }

      return updatedProject;
    } catch (err) {
      toast.error('Failed to update project');
      return null;
    }
  }, [projectId]);

  const deleteProject = useCallback(async (): Promise<boolean> => {
    if (!projectId) return false;

    try {
      const { error } = await ProjectsService.deleteProject(projectId);
      
      if (error) {
        toast.error('Failed to delete project');
        return false;
      }

      toast.success('Project deleted successfully');
      return true;
    } catch (err) {
      toast.error('Failed to delete project');
      return false;
    }
  }, [projectId]);

  const toggleFavorite = useCallback(async (): Promise<Project | null> => {
    if (!projectId) return null;

    try {
      const { project: updatedProject, error } = await ProjectsService.toggleProjectFavorite(projectId);
      
      if (error) {
        toast.error('Failed to update favorite status');
        return null;
      }

      if (updatedProject) {
        setProject(updatedProject);
        const isFavorite = updatedProject.content.metadata?.isFavorite;
        toast.success(isFavorite ? 'Added to favorites' : 'Removed from favorites');
      }

      return updatedProject;
    } catch (err) {
      toast.error('Failed to update favorite status');
      return null;
    }
  }, [projectId]);

  const addIteration = useCallback(async (
    userIdea: string, 
    selectedStyle: string, 
    generatedPrompt: string
  ): Promise<Project | null> => {
    if (!projectId) return null;

    try {
      const { project: updatedProject, error } = await ProjectsService.addProjectIteration(
        projectId, 
        userIdea, 
        selectedStyle, 
        generatedPrompt
      );
      
      if (error) {
        toast.error('Failed to add iteration');
        return null;
      }

      if (updatedProject) {
        setProject(updatedProject);
        toast.success('Iteration added successfully');
      }

      return updatedProject;
    } catch (err) {
      toast.error('Failed to add iteration');
      return null;
    }
  }, [projectId]);

  const refreshProject = useCallback(async () => {
    await fetchProject();
  }, [fetchProject]);

  return {
    project,
    loading,
    error,
    updateProject,
    deleteProject,
    toggleFavorite,
    addIteration,
    refreshProject
  };
};
