-- ChatGPT-style Projects and Chats Schema
-- This migration redesigns the database to match <PERSON>t<PERSON><PERSON>'s interface pattern

-- Drop existing projects table to redesign it
DROP TABLE IF EXISTS public.projects CASCADE;

-- Create projects table (containers for multiple chats)
CREATE TABLE public.projects (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    custom_instructions TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create chats table (individual conversations within projects or standalone)
CREATE TABLE public.chats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    last_message_preview TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create messages table (individual messages within chats)
CREATE TABLE public.messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create project_files table (files uploaded to projects)
CREATE TABLE public.project_files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    storage_path TEXT NOT NULL,
    extracted_content TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create chat_files table (files attached to specific chats)
CREATE TABLE public.chat_files (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    chat_id UUID REFERENCES public.chats(id) ON DELETE CASCADE NOT NULL,
    project_file_id UUID REFERENCES public.project_files(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create prompt_templates table (for different prompt styles)
CREATE TABLE public.prompt_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    template_content TEXT NOT NULL,
    is_system_template BOOLEAN DEFAULT TRUE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_projects_user_id ON public.projects(user_id);
CREATE INDEX idx_projects_created_at ON public.projects(created_at DESC);

CREATE INDEX idx_chats_user_id ON public.chats(user_id);
CREATE INDEX idx_chats_project_id ON public.chats(project_id);
CREATE INDEX idx_chats_updated_at ON public.chats(updated_at DESC);

CREATE INDEX idx_messages_chat_id ON public.messages(chat_id);
CREATE INDEX idx_messages_created_at ON public.messages(created_at);

CREATE INDEX idx_project_files_project_id ON public.project_files(project_id);
CREATE INDEX idx_project_files_user_id ON public.project_files(user_id);

CREATE INDEX idx_chat_files_chat_id ON public.chat_files(chat_id);

CREATE INDEX idx_prompt_templates_user_id ON public.prompt_templates(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.prompt_templates ENABLE ROW LEVEL SECURITY;

-- RLS Policies for projects
CREATE POLICY "Users can view their own projects" ON public.projects
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own projects" ON public.projects
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own projects" ON public.projects
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own projects" ON public.projects
    FOR DELETE USING (auth.uid() = user_id);

-- Users can view public projects
CREATE POLICY "Anyone can view public projects" ON public.projects
    FOR SELECT USING (is_public = true);

-- RLS Policies for chats
CREATE POLICY "Users can view their own chats" ON public.chats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own chats" ON public.chats
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own chats" ON public.chats
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own chats" ON public.chats
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for messages
CREATE POLICY "Users can view messages in their chats" ON public.messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.chats 
            WHERE chats.id = messages.chat_id AND chats.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert messages in their chats" ON public.messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.chats 
            WHERE chats.id = messages.chat_id AND chats.user_id = auth.uid()
        )
    );

-- RLS Policies for project_files
CREATE POLICY "Users can view their own project files" ON public.project_files
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own project files" ON public.project_files
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own project files" ON public.project_files
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own project files" ON public.project_files
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for chat_files
CREATE POLICY "Users can view chat files in their chats" ON public.chat_files
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.chats 
            WHERE chats.id = chat_files.chat_id AND chats.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert chat files in their chats" ON public.chat_files
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.chats 
            WHERE chats.id = chat_files.chat_id AND chats.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete chat files in their chats" ON public.chat_files
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.chats 
            WHERE chats.id = chat_files.chat_id AND chats.user_id = auth.uid()
        )
    );

-- RLS Policies for prompt_templates
CREATE POLICY "Users can view system templates and their own templates" ON public.prompt_templates
    FOR SELECT USING (is_system_template = true OR auth.uid() = user_id);

CREATE POLICY "Users can insert their own templates" ON public.prompt_templates
    FOR INSERT WITH CHECK (auth.uid() = user_id AND is_system_template = false);

CREATE POLICY "Users can update their own templates" ON public.prompt_templates
    FOR UPDATE USING (auth.uid() = user_id AND is_system_template = false);

CREATE POLICY "Users can delete their own templates" ON public.prompt_templates
    FOR DELETE USING (auth.uid() = user_id AND is_system_template = false);

-- Admin policies
CREATE POLICY "Admins can view all projects" ON public.projects
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND subscription_status = 'admin'
        )
    );

CREATE POLICY "Admins can view all chats" ON public.chats
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND subscription_status = 'admin'
        )
    );

-- Create triggers for updated_at
CREATE TRIGGER update_projects_updated_at
    BEFORE UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_chats_updated_at
    BEFORE UPDATE ON public.chats
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default prompt templates
INSERT INTO public.prompt_templates (name, description, template_content, is_system_template) VALUES
('Technical', 'Structured, detailed instructions for technical projects', 'Create a {project_type} with the following specifications:\n\n## Core Features\n- Feature 1: [Primary functionality]\n- Feature 2: [Secondary functionality]\n- Feature 3: [Additional functionality]\n\n## Technical Requirements\n- Use modern, maintainable code architecture\n- Implement responsive design for all device sizes\n- Ensure accessibility compliance\n- Optimize for performance\n\n## Acceptance Criteria\n- The solution must be fully functional and meet all core requirements\n- Code should be well-documented and follow best practices\n- Include comprehensive test coverage', true),

('Creative', 'Open-ended, imaginative prompts for creative work', 'Create a {project_type} that:\n\n## Vision\n- Captures the essence of [core concept]\n- Evokes [desired emotion/feeling]\n- Appeals to [target audience]\n\n## Creative Elements\n- Style: [aesthetic direction]\n- Tone: [voice and personality]\n- Unique features: [what makes it special]\n\n## Inspiration Sources\n- Draw from [relevant industry examples]\n- Incorporate elements of [cultural references]\n- Put a fresh spin on traditional concepts', true),

('Business', 'Goal-oriented prompts focused on business outcomes', 'Develop a {project_type} that solves [specific business problem] and delivers measurable ROI.\n\n## Target Audience\n- Primary: [main demographic]\n- Secondary: [additional demographic]\n- Customer needs: [pain points to address]\n\n## Business Goals\n- Increase [key metric] by [target percentage]\n- Reduce [pain point] by [target amount]\n- Generate [specific outcome] within [timeframe]\n\n## Success Metrics\n- KPI 1: [specific measurement]\n- KPI 2: [specific measurement]\n- Timeline: [project milestones]', true),

('Concise', 'Brief, to-the-point instructions with minimal detail', 'Build a {project_type}.\n\nMake it:\n- Simple & intuitive\n- Visually appealing\n- Functional & useful\n\nInclude essential features only. Focus on quality over quantity.', true),

('Detailed', 'Comprehensive instructions with extensive specifications', '# Comprehensive {project_type} Specification\n\n## Project Overview and Objectives\nCreate a fully-featured {project_type} that addresses [specific problem] for [target users]. The solution should be comprehensive, polished, and ready for deployment.\n\n## Detailed Requirements\n\n### Functional Requirements\n1. Core Functionality\n   - Feature A: [detailed description]\n   - Feature B: [detailed description]\n   - Feature C: [detailed description]\n\n### Technical Specifications\n- Architecture: [system design]\n- Performance: [speed and efficiency requirements]\n- Security: [data protection and access control]\n- Scalability: [growth and expansion considerations]\n\n### User Experience\n- Interface design: [visual and interaction guidelines]\n- Accessibility: [compliance and inclusive design]\n- Mobile responsiveness: [cross-device compatibility]\n\n## Implementation Plan\n1. Phase 1: [initial development]\n2. Phase 2: [feature expansion]\n3. Phase 3: [optimization and deployment]\n\n## Quality Assurance\n- Testing strategy: [comprehensive test coverage]\n- Documentation: [user guides and technical docs]\n- Maintenance: [ongoing support and updates]', true);

-- Create function to automatically update chat title and preview
CREATE OR REPLACE FUNCTION update_chat_on_message_insert()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the chat's updated_at timestamp and last message preview
    UPDATE public.chats 
    SET 
        updated_at = NOW(),
        last_message_preview = CASE 
            WHEN NEW.role = 'user' THEN LEFT(NEW.content, 100)
            ELSE last_message_preview
        END,
        title = CASE 
            WHEN title = 'New Chat' AND NEW.role = 'user' THEN LEFT(NEW.content, 50)
            ELSE title
        END
    WHERE id = NEW.chat_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for auto-updating chat info
CREATE TRIGGER on_message_insert
    AFTER INSERT ON public.messages
    FOR EACH ROW EXECUTE FUNCTION update_chat_on_message_insert();

-- Create storage bucket for project files
INSERT INTO storage.buckets (id, name, public) VALUES ('project-files', 'project-files', false);

-- Storage policies for project files
CREATE POLICY "Users can upload their own project files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'project-files' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their own project files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'project-files' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own project files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'project-files' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );
