import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { FileService } from '@/lib/fileService';
import { ProjectFile } from '@/lib/supabase';
import { 
  Upload, 
  X, 
  File, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  Paperclip
} from 'lucide-react';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

interface FileUploadProps {
  projectId: string;
  onFileUploaded?: (file: ProjectFile) => void;
  maxFiles?: number;
  compact?: boolean;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
  result?: ProjectFile;
}

const FileUpload: React.FC<FileUploadProps> = ({
  projectId,
  onFileUploaded,
  maxFiles = 10,
  compact = false
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const fileArray = Array.from(files);
    
    // Check max files limit
    if (uploadingFiles.length + fileArray.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }

    // Add files to uploading state
    const newUploadingFiles: UploadingFile[] = fileArray.map(file => ({
      file,
      progress: 0,
      status: 'uploading'
    }));

    setUploadingFiles(prev => [...prev, ...newUploadingFiles]);

    // Upload each file
    fileArray.forEach((file, index) => {
      uploadFile(file, uploadingFiles.length + index);
    });
  };

  const uploadFile = async (file: File, index: number) => {
    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadingFiles(prev => prev.map((item, i) => 
          i === index && item.status === 'uploading'
            ? { ...item, progress: Math.min(item.progress + 10, 90) }
            : item
        ));
      }, 200);

      const { file: uploadedFile, error } = await FileService.uploadFile({
        file,
        projectId
      });

      clearInterval(progressInterval);

      if (error || !uploadedFile) {
        setUploadingFiles(prev => prev.map((item, i) => 
          i === index 
            ? { ...item, status: 'error', progress: 100, error: error?.message || 'Upload failed' }
            : item
        ));
        toast.error(`Failed to upload ${file.name}`);
        return;
      }

      setUploadingFiles(prev => prev.map((item, i) => 
        i === index 
          ? { ...item, status: 'success', progress: 100, result: uploadedFile }
          : item
      ));

      onFileUploaded?.(uploadedFile);
      toast.success(`${file.name} uploaded successfully`);

    } catch (error) {
      console.error('Upload error:', error);
      setUploadingFiles(prev => prev.map((item, i) => 
        i === index 
          ? { ...item, status: 'error', progress: 100, error: 'Upload failed' }
          : item
      ));
      toast.error(`Failed to upload ${file.name}`);
    }
  };

  const removeUploadingFile = (index: number) => {
    setUploadingFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  if (compact) {
    return (
      <div className="relative">
        <Button
          variant="ghost"
          size="icon"
          onClick={openFileDialog}
          className="h-8 w-8"
        >
          <Paperclip className="w-4 h-4" />
        </Button>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={(e) => handleFileSelect(e.target.files)}
          accept=".pdf,.doc,.docx,.txt,.md,.csv,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.webp,.json,.html,.rtf"
        />

        {/* Upload Progress Overlay */}
        <AnimatePresence>
          {uploadingFiles.length > 0 && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="absolute top-full left-0 mt-2 z-50"
            >
              <Card className="w-80 shadow-lg">
                <CardContent className="p-3">
                  <div className="space-y-2">
                    {uploadingFiles.map((item, index) => (
                      <UploadItem
                        key={index}
                        item={item}
                        onRemove={() => removeUploadingFile(index)}
                      />
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Drop Zone */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Upload className="w-8 h-8 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium mb-2">Upload Files</h3>
        <p className="text-gray-600 mb-4">
          Drag and drop files here, or click to browse
        </p>
        <Button onClick={openFileDialog} variant="outline">
          <File className="w-4 h-4 mr-2" />
          Choose Files
        </Button>
        
        <div className="mt-4 text-xs text-gray-500">
          <p>Supported: PDF, Word, Excel, Images, Text files</p>
          <p>Max file size: 50MB • Max {maxFiles} files</p>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={(e) => handleFileSelect(e.target.files)}
        accept=".pdf,.doc,.docx,.txt,.md,.csv,.xls,.xlsx,.jpg,.jpeg,.png,.gif,.webp,.json,.html,.rtf"
      />

      {/* Upload Progress */}
      <AnimatePresence>
        {uploadingFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="space-y-2"
          >
            {uploadingFiles.map((item, index) => (
              <UploadItem
                key={index}
                item={item}
                onRemove={() => removeUploadingFile(index)}
              />
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

interface UploadItemProps {
  item: UploadingFile;
  onRemove: () => void;
}

const UploadItem: React.FC<UploadItemProps> = ({ item, onRemove }) => {
  const getStatusIcon = () => {
    switch (item.status) {
      case 'uploading':
        return <Loader2 className="w-4 h-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusColor = () => {
    switch (item.status) {
      case 'uploading':
        return 'bg-blue-500';
      case 'success':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg"
    >
      <div className="flex-shrink-0">
        {getStatusIcon()}
      </div>

      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <p className="text-sm font-medium truncate">{item.file.name}</p>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={onRemove}
          >
            <X className="w-3 h-3" />
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex-1">
            <Progress value={item.progress} className="h-1" />
          </div>
          <span className="text-xs text-gray-500">
            {FileService.formatFileSize(item.file.size)}
          </span>
        </div>

        {item.status === 'error' && item.error && (
          <p className="text-xs text-red-600 mt-1">{item.error}</p>
        )}
      </div>
    </motion.div>
  );
};

export default FileUpload;
