import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChatService } from '@/lib/chatService';
import { Message, Chat, Project, ProjectFile, supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import FileUpload from '@/components/FileUpload';
import { 
  Send, 
  Paperclip, 
  MoreVertical, 
  User, 
  Bot, 
  Loader2,
  Settings,
  FileText,
  Image,
  Upload
} from 'lucide-react';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';

interface ChatInterfaceProps {
  chatId?: string;
  projectId?: string;
  onChatCreated?: (chatId: string) => void;
}

const promptStyles = [
  { id: 'technical', name: 'Technical', description: 'Structured, detailed instructions' },
  { id: 'creative', name: 'Creative', description: 'Open-ended, imaginative prompts' },
  { id: 'business', name: 'Business', description: 'Goal-oriented, business-focused' },
  { id: 'concise', name: 'Concise', description: 'Brief, to-the-point instructions' },
  { id: 'detailed', name: 'Detailed', description: 'Comprehensive specifications' }
];

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  chatId,
  projectId,
  onChatCreated
}) => {
  const navigate = useNavigate();
  const { user, profile, isAuthenticated } = useAuth();
  const [chat, setChat] = useState<Chat | null>(null);
  const [project, setProject] = useState<Project | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('technical');
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [projectFiles, setProjectFiles] = useState<ProjectFile[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Check if user has premium access
  const hasPremiumAccess = () => {
    return isAuthenticated && (
      profile?.subscription_status === 'premium' ||
      profile?.subscription_status === 'admin' ||
      user?.email === '<EMAIL>' // Admin override
    );
  };

  // Load chat data
  useEffect(() => {
    if (chatId) {
      loadChat(chatId);
    } else if (projectId) {
      loadProject(projectId);
    }
  }, [chatId, projectId]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const loadChat = async (id: string) => {
    setIsLoading(true);
    try {
      const { chat: chatData, messages: messagesData, error } = await ChatService.getChat(id);
      
      if (error) {
        toast.error('Failed to load chat');
        return;
      }

      setChat(chatData);
      setMessages(messagesData);

      // Load project if chat belongs to one
      if (chatData?.project_id) {
        loadProject(chatData.project_id);
      }
    } catch (error) {
      console.error('Error loading chat:', error);
      toast.error('Failed to load chat');
    } finally {
      setIsLoading(false);
    }
  };

  const loadProject = async (id: string) => {
    try {
      const { data: projectData, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error loading project:', error);
        return;
      }

      setProject(projectData);
    } catch (error) {
      console.error('Error loading project:', error);
    }
  };

  const createNewChat = async () => {
    try {
      const { chat: newChat, error } = await ChatService.createChat({
        title: 'New Chat',
        projectId: projectId
      });

      if (error || !newChat) {
        toast.error('Failed to create chat');
        return;
      }

      setChat(newChat);
      setMessages([]);
      onChatCreated?.(newChat.id);
    } catch (error) {
      console.error('Error creating chat:', error);
      toast.error('Failed to create chat');
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || isGenerating) return;

    // Strict paywall: Only allow premium users to send messages
    if (!hasPremiumAccess()) {
      if (!isAuthenticated) {
        toast.error('Please log in and subscribe to use the chat', {
          action: {
            label: 'Login & Subscribe',
            onClick: () => navigate('/auth')
          }
        });
      } else {
        toast.error('Premium subscription required to use chat', {
          action: {
            label: 'Subscribe Now',
            onClick: () => navigate('/pricing')
          }
        });
      }
      return;
    }

    let currentChatId = chatId;

    // Create new chat if none exists
    if (!currentChatId && !chat) {
      const { chat: newChat, error } = await ChatService.createChat({
        title: inputMessage.substring(0, 50),
        projectId: projectId
      });

      if (error || !newChat) {
        toast.error('Failed to create chat');
        return;
      }

      setChat(newChat);
      currentChatId = newChat.id;
      onChatCreated?.(newChat.id);
    } else if (chat) {
      currentChatId = chat.id;
    }

    if (!currentChatId) return;

    const userMessageContent = inputMessage;
    setInputMessage('');
    setIsGenerating(true);

    try {
      // Send user message
      const { message: userMessage, error: userError } = await ChatService.sendMessage({
        chatId: currentChatId,
        content: userMessageContent,
        role: 'user'
      });

      if (userError || !userMessage) {
        toast.error('Failed to send message');
        setIsGenerating(false);
        return;
      }

      // Add user message to UI immediately
      setMessages(prev => [...prev, userMessage]);

      // Generate AI response
      const { message: aiMessage, error: aiError } = await ChatService.generateResponse(
        currentChatId,
        userMessageContent,
        selectedStyle
      );

      if (aiError || !aiMessage) {
        toast.error('Failed to generate response');
        setIsGenerating(false);
        return;
      }

      // Add AI message to UI
      setMessages(prev => [...prev, aiMessage]);

    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading chat...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold">
              {chat?.title || 'New Chat'}
            </h1>
            {project && (
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-xs">
                  {project.name}
                </Badge>
                {project.custom_instructions && (
                  <Badge variant="secondary" className="text-xs">
                    Custom Instructions
                  </Badge>
                )}
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Select value={selectedStyle} onValueChange={setSelectedStyle}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {promptStyles.map((style) => (
                  <SelectItem key={style.id} value={style.id}>
                    {style.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button variant="ghost" size="icon">
              <MoreVertical className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4 max-w-4xl mx-auto">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <Bot className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">Start a conversation</h3>
              <p className="text-gray-600 mb-4">
                {project 
                  ? `Ask questions or give instructions related to "${project.name}"`
                  : 'Ask me anything or give me a task to help you with'
                }
              </p>
              {project?.custom_instructions && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 max-w-md mx-auto">
                  <strong>Project Instructions:</strong>
                  <p className="mt-1">{project.custom_instructions}</p>
                </div>
              )}
            </div>
          ) : (
            <AnimatePresence>
              {messages.map((message, index) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <MessageBubble message={message} />
                </motion.div>
              ))}
            </AnimatePresence>
          )}

          {isGenerating && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex items-start gap-3"
            >
              <Avatar className="w-8 h-8">
                <AvatarFallback>
                  <Bot className="w-4 h-4" />
                </AvatarFallback>
              </Avatar>
              <div className="bg-gray-100 rounded-lg p-3 max-w-3xl">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="text-sm text-gray-600">Thinking...</span>
                </div>
              </div>
            </motion.div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="border-t border-gray-200 p-4">
        <div className="max-w-4xl mx-auto">
          {/* Remove the premium banner completely */}

          <div className="flex items-end gap-2">
            <div className="flex-1 relative">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={
                  project
                    ? `Message ${project.name}...`
                    : "Type your message..."
                }
                className="pr-20 min-h-[44px] resize-none"
                disabled={isGenerating}
              />
              
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                {project && (
                  <FileUpload
                    projectId={project.id}
                    onFileUploaded={(file) => {
                      setProjectFiles(prev => [file, ...prev]);
                      toast.success('File uploaded successfully');
                    }}
                    compact
                  />
                )}
              </div>
            </div>

            <Button
              onClick={sendMessage}
              disabled={!inputMessage.trim() || isGenerating}
              className="h-11"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>

          <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
            <span>
              {project ? `Project: ${project.name}` : 'Standalone chat'}
            </span>
            <span>
              Style: {promptStyles.find(s => s.id === selectedStyle)?.name}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

interface MessageBubbleProps {
  message: Message;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isUser = message.role === 'user';

  return (
    <div className={`flex items-start gap-3 ${isUser ? 'flex-row-reverse' : ''}`}>
      <Avatar className="w-8 h-8">
        <AvatarFallback>
          {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
        </AvatarFallback>
      </Avatar>

      <div className={`max-w-3xl ${isUser ? 'text-right' : ''}`}>
        <div
          className={`rounded-lg p-3 ${
            isUser 
              ? 'bg-blue-600 text-white ml-auto' 
              : 'bg-gray-100 text-gray-900'
          }`}
        >
          {isUser ? (
            <p className="whitespace-pre-wrap">{message.content}</p>
          ) : (
            <div className="prose prose-sm max-w-none">
              <ReactMarkdown>{message.content}</ReactMarkdown>
            </div>
          )}
        </div>
        
        <div className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right' : ''}`}>
          {new Date(message.created_at).toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
