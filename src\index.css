
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    --blue-gradient-start: #4169E1;
    --blue-gradient-end: rgba(65, 105, 225, 0.2);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .text-highlight {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-highlight to-blue-500;
  }
  
  .input-focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-highlight/30 focus:border-highlight/50 transition-all duration-300;
  }
  
  .glass-panel {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg;
  }
  
  .btn-primary {
    @apply px-6 py-3 bg-gradient-to-r from-highlight to-blue-500 text-white rounded-full shadow-md hover:shadow-lg transition-all duration-300 ease-in-out;
  }
  
  .btn-secondary {
    @apply px-6 py-3 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full shadow-md hover:shadow-lg transition-all duration-300 ease-in-out;
  }

  .animate-enter {
    @apply opacity-0 translate-y-4;
    animation: enter 0.6s ease-out forwards;
  }

  @keyframes enter {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-enter-delay-1 {
    animation-delay: 0.1s;
  }

  .animate-enter-delay-2 {
    animation-delay: 0.2s;
  }

  .animate-enter-delay-3 {
    animation-delay: 0.3s;
  }

  .prompt-input {
    @apply relative w-full transition-all duration-300;
  }

  .prompt-input-textarea {
    @apply w-full min-h-[120px] p-4 text-lg rounded-xl
           bg-white/80 backdrop-blur-sm
           border-2 border-blue-500/20
           focus:border-blue-500/40 focus:ring-2 focus:ring-blue-500/30
           shadow-lg resize-none;
  }

  .prompt-input-button {
    @apply absolute right-2 bottom-2
           px-6 py-2.5 
           bg-gradient-to-r from-blue-500 to-blue-600
           hover:from-blue-600 hover:to-blue-700
           text-white font-medium rounded-lg
           shadow-md transition-all duration-300
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
}
