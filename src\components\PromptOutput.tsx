
import React, { useRef, useState } from 'react';
import { Check, Co<PERSON>, Sparkles, Download, Save } from 'lucide-react';
import { toast } from 'sonner';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useAuth } from '@/contexts/AuthContext';
import SaveProjectDialog from './SaveProjectDialog';

interface PromptOutputProps {
  content: string;
  userIdea: string;
  selectedStyle: string;
  currentProjectId?: string;
  onProjectSaved?: (projectId: string) => void;
}

const PromptOutput: React.FC<PromptOutputProps> = ({
  content,
  userIdea,
  selectedStyle,
  currentProjectId,
  onProjectSaved
}) => {
  const [copied, setCopied] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);
  const { isAuthenticated } = useAuth();

  const copyToClipboard = async () => {
    if (!content) return;
    await navigator.clipboard.writeText(content);
    setCopied(true);
    toast.success('Copied to clipboard');
    setTimeout(() => setCopied(false), 2000);
  };

  const handleDownloadPDF = async () => {
    if (!textRef.current) return;
    try {
      const canvas = await html2canvas(textRef.current, {
        scale: 2,
        logging: false,
        useCORS: true
      });
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      const imgWidth = 210;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      pdf.save('generated-prompt.pdf');
      toast.success('PDF downloaded successfully');
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      toast.error('Failed to download PDF');
    }
  };

  const handleSaveProject = () => {
    if (!isAuthenticated) {
      toast.error('Please sign in to save projects');
      return;
    }
    setShowSaveDialog(true);
  };

  const handleProjectSaved = (projectId: string) => {
    setShowSaveDialog(false);
    onProjectSaved?.(projectId);
  };

  if (!content) return null;

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mt-8"
    >
      <div className="relative bg-white/90 backdrop-blur-sm rounded-2xl border border-blue-100 shadow-xl overflow-visible">
        {/* Header with gradient */}
        <div className="absolute -top-3 left-4 z-20">
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="flex items-center gap-2 px-4 py-1.5 bg-gradient-to-r from-blue-600 to-blue-400 rounded-full shadow-lg"
          >
            <Sparkles className="w-4 h-4 text-white animate-pulse" />
            <span className="text-sm font-medium text-white">Generated Prompt</span>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <div className="absolute top-4 right-4 flex gap-2 z-20">
          {isAuthenticated && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleSaveProject}
              className="p-2.5 rounded-xl bg-white/80 backdrop-blur-sm hover:bg-white/90 transition-all duration-300 shadow-md hover:shadow-lg border border-blue-100 group"
              aria-label="Save project"
            >
              <Save className="w-5 h-5 text-blue-500 group-hover:scale-110 transition-transform duration-300" />
            </motion.button>
          )}

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={handleDownloadPDF}
            className="p-2.5 rounded-xl bg-white/80 backdrop-blur-sm hover:bg-white/90 transition-all duration-300 shadow-md hover:shadow-lg border border-blue-100 group"
            aria-label="Download PDF"
          >
            <Download className="w-5 h-5 text-blue-500 group-hover:scale-110 transition-transform duration-300" />
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={copyToClipboard}
            className="p-2.5 rounded-xl bg-white/80 backdrop-blur-sm hover:bg-white/90 transition-all duration-300 shadow-md hover:shadow-lg border border-blue-100 group"
            aria-label="Copy to clipboard"
          >
            {copied ? (
              <Check className="w-5 h-5 text-green-500" />
            ) : (
              <Copy className="w-5 h-5 text-blue-500 group-hover:scale-110 transition-transform duration-300" />
            )}
          </motion.button>
        </div>

        {/* Content */}
        <div className="pt-12 px-6 pb-6">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            ref={textRef}
            className="prose prose-blue max-w-none overflow-auto max-h-[60vh]"
            style={{
              fontSize: '1.05rem',
              scrollbarWidth: 'thin',
              scrollbarColor: '#93C5FD transparent'
            }}
          >
            <ReactMarkdown
              components={{
                h1: ({node, ...props}) => (
                  <motion.h1 
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="text-2xl font-bold text-blue-900 mb-4 text-left border-b-2 border-blue-200 pb-2"
                    {...props}
                  />
                ),
                h2: ({node, ...props}) => (
                  <motion.h2
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="text-xl font-semibold text-blue-800 mb-3 text-left border-l-4 border-blue-400 pl-3"
                    {...props}
                  />
                ),
                h3: ({node, ...props}) => (
                  <motion.h3
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="text-lg font-medium text-blue-700 mb-2 text-left"
                    {...props}
                  />
                ),
                h4: ({node, ...props}) => (
                  <motion.h4
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="text-base font-medium text-blue-600 mb-2 text-left border-l-2 border-blue-300 pl-3"
                    {...props}
                  />
                ),
                p: ({node, ...props}) => (
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="mb-4 text-gray-700 text-left leading-relaxed"
                    {...props}
                  />
                ),
                ul: ({node, ...props}) => (
                  <motion.ul
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="list-none space-y-3 mb-4 text-left pl-2"
                    {...props}
                  />
                ),
                li: ({node, ...props}) => (
                  <motion.li
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-start gap-3 text-left"
                    {...props}
                  >
                    <span className="mt-2 w-2 h-2 rounded-full bg-blue-400 flex-shrink-0" />
                    <span className="flex-1">{props.children}</span>
                  </motion.li>
                ),
                blockquote: ({node, ...props}) => (
                  <motion.blockquote
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="border-l-4 border-blue-400 bg-blue-50/80 pl-4 py-3 my-4 text-left italic text-gray-700 rounded-lg shadow-sm"
                    {...props}
                  />
                ),
                code: ({node, inline, ...props}) => (
                  inline ?
                    <code className="px-1.5 py-0.5 rounded bg-gray-100 text-blue-600 font-mono text-sm" {...props} /> :
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="bg-gray-50 rounded-lg p-4 my-4 overflow-x-auto shadow-inner"
                    >
                      <code className="block text-sm font-mono text-gray-800" {...props} />
                    </motion.div>
                ),
                span: ({node, ...props}) => {
                  const isBadge = props.children?.[0]?.toString()?.match(/^\[.*\]$/);
                  if (isBadge) {
                    return (
                      <motion.span
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200 shadow-sm mx-1"
                        {...props}
                      />
                    );
                  }
                  return <span {...props} />;
                },
              }}
            >
              {content}
            </ReactMarkdown>
          </motion.div>
        </div>
      </div>

      {/* Save Project Dialog */}
      <SaveProjectDialog
        open={showSaveDialog}
        onOpenChange={setShowSaveDialog}
        userIdea={userIdea}
        selectedStyle={selectedStyle}
        generatedPrompt={content}
        currentProjectId={currentProjectId}
        onProjectSaved={handleProjectSaved}
      />
    </motion.div>
  );
};

export default PromptOutput;
