
import React from 'react';
import { Send } from 'lucide-react';

interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  isDisabled?: boolean;
}

const PromptInput: React.FC<PromptInputProps> = ({
  value,
  onChange,
  onSubmit,
  isDisabled
}) => {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSubmit();
    }
  };

  return (
    <div className="prompt-input mt-4 relative">
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder="Build me a calculator app..."
        disabled={isDisabled}
        className="w-full h-20 px-8 pr-24 rounded-full shadow-lg bg-white/80 backdrop-blur-sm border-2 border-blue-500/20 focus:outline-none focus:border-blue-500/40 focus:ring-2 focus:ring-blue-500/30 transition-all text-lg resize-none py-6 text-left placeholder:text-gray-400 placeholder:text-left"
      />
      <button
        onClick={onSubmit}
        disabled={isDisabled}
        className="absolute right-4 top-1/2 -translate-y-1/2 px-6 py-2.5 rounded-full bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
      >
        <Send className="w-4 h-4" />
        <span>Send</span>
      </button>
    </div>
  );
};

export default PromptInput;
