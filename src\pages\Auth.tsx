import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import BackgroundEffects from '@/components/BackgroundEffects';
import Logo from '@/components/Logo';
import { User, Lock, Mail, UserPlus, KeyRound, Sparkles, Shield, Zap, ArrowLeft } from 'lucide-react';

const Auth = () => {
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [signupForm, setSignupForm] = useState({ email: '', password: '', fullName: '', confirmPassword: '' });
  const [resetForm, setResetForm] = useState({ email: '' });
  const [loading, setLoading] = useState(false);
  const [showResetForm, setShowResetForm] = useState(false);
  const { signIn, signUp, resetPassword, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // Clear loading state if user becomes authenticated (successful sign-in)
  useEffect(() => {
    if (isAuthenticated) {
      setLoading(false);
    }
  }, [isAuthenticated]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!loginForm.email.trim() || !loginForm.password.trim()) {
      toast.error('Please enter both email and password');
      return;
    }

    setLoading(true);
    const { error } = await signIn(loginForm.email, loginForm.password);

    if (error) {
      toast.error(error.message || 'Login failed');
      setLoading(false); // Only reset loading on error
    } else {
      toast.success('Login successful');
      // Don't reset loading here - let the useEffect handle it when isAuthenticated becomes true
      // This prevents the loading state from flickering
    }
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!signupForm.email.trim() || !signupForm.password.trim() || !signupForm.fullName.trim()) {
      toast.error('Please fill in all fields');
      return;
    }
    
    if (signupForm.password !== signupForm.confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }
    
    if (signupForm.password.length < 6) {
      toast.error('Password must be at least 6 characters');
      return;
    }
    
    setLoading(true);
    const { error } = await signUp(signupForm.email, signupForm.password, signupForm.fullName);
    
    if (error) {
      toast.error(error.message || 'Signup failed');
    } else {
      toast.success('Account created! Please check your email to verify your account.');
    }
    
    setLoading(false);
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!resetForm.email.trim()) {
      toast.error('Please enter your email address');
      return;
    }
    
    setLoading(true);
    const { error } = await resetPassword(resetForm.email);
    
    if (error) {
      toast.error(error.message || 'Failed to send reset email');
    } else {
      toast.success('Password reset email sent! Check your inbox.');
      setShowResetForm(false);
      setResetForm({ email: '' });
    }
    
    setLoading(false);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundEffects />
      
      <div className="relative z-10 min-h-screen flex">
        {/* Left Column - Instructions & Branding */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center px-12 py-16">
          <div className="max-w-lg">
            <Logo className="mb-8" />
            
            <h1 className="text-4xl font-bold tracking-tight mb-6">
              Welcome to <span className="text-highlight">ChatsPrompt</span>
            </h1>
            
            <p className="text-lg text-gray-600 mb-8">
              Transform your ideas into powerful prompts with our AI-powered platform. 
              Join thousands of creators who are already building amazing content.
            </p>
            
            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-gradient-to-tr from-highlight to-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <Sparkles className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">AI-Powered Prompts</h3>
                  <p className="text-gray-600">Generate high-quality prompts for any use case with our advanced AI technology.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-gradient-to-tr from-highlight to-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">Secure & Private</h3>
                  <p className="text-gray-600">Your data is protected with enterprise-grade security and privacy measures.</p>
                </div>
              </div>
              
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-gradient-to-tr from-highlight to-blue-400 rounded-full flex items-center justify-center flex-shrink-0">
                  <Zap className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">Lightning Fast</h3>
                  <p className="text-gray-600">Get instant results and save your projects for future reference.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Right Column - Auth Forms */}
        <div className="w-full lg:w-1/2 flex items-center justify-center px-6 py-16">
          <div className="w-full max-w-md">
            {/* Back Button */}
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              className="mb-6 flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </Button>

            <div className="lg:hidden mb-8 text-center">
              <Logo className="mx-auto mb-4" />
              <h1 className="text-2xl font-bold">
                Welcome to <span className="text-highlight">ChatsPrompt</span>
              </h1>
            </div>
            
            <Card className="glass-panel shadow-2xl">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">Get Started</CardTitle>
                <CardDescription>
                  Sign in to your account or create a new one
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                {!showResetForm ? (
                  <Tabs defaultValue="login" className="w-full">
                    <TabsList className="grid w-full grid-cols-2">
                      <TabsTrigger value="login">Sign In</TabsTrigger>
                      <TabsTrigger value="signup">Sign Up</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="login" className="space-y-4 mt-6">
                      <form onSubmit={handleLogin} className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium leading-none flex items-center gap-2">
                            <Mail className="w-4 h-4" />
                            Email
                          </label>
                          <Input
                            type="email"
                            placeholder="Enter your email"
                            value={loginForm.email}
                            onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                            className="input-focus-ring bg-white/50"
                            required
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-sm font-medium leading-none flex items-center gap-2">
                            <Lock className="w-4 h-4" />
                            Password
                          </label>
                          <Input
                            type="password"
                            placeholder="Enter your password"
                            value={loginForm.password}
                            onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                            className="input-focus-ring bg-white/50"
                            required
                          />
                        </div>
                        
                        <Button 
                          type="submit" 
                          className="btn-primary w-full"
                          disabled={loading}
                        >
                          {loading ? 'Signing In...' : 'Sign In'}
                        </Button>
                      </form>

                      <div className="text-center mt-4">
                        <button
                          type="button"
                          onClick={() => setShowResetForm(true)}
                          className="text-sm text-highlight hover:underline"
                        >
                          Forgot your password?
                        </button>
                      </div>
                    </TabsContent>
                    
                    <TabsContent value="signup" className="space-y-4 mt-6">
                      <form onSubmit={handleSignup} className="space-y-4">
                        <div className="space-y-2">
                          <label className="text-sm font-medium leading-none flex items-center gap-2">
                            <User className="w-4 h-4" />
                            Full Name
                          </label>
                          <Input
                            type="text"
                            placeholder="Enter your full name"
                            value={signupForm.fullName}
                            onChange={(e) => setSignupForm({ ...signupForm, fullName: e.target.value })}
                            className="input-focus-ring bg-white/50"
                            required
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-sm font-medium leading-none flex items-center gap-2">
                            <Mail className="w-4 h-4" />
                            Email
                          </label>
                          <Input
                            type="email"
                            placeholder="Enter your email"
                            value={signupForm.email}
                            onChange={(e) => setSignupForm({ ...signupForm, email: e.target.value })}
                            className="input-focus-ring bg-white/50"
                            required
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-sm font-medium leading-none flex items-center gap-2">
                            <Lock className="w-4 h-4" />
                            Password
                          </label>
                          <Input
                            type="password"
                            placeholder="Create a password"
                            value={signupForm.password}
                            onChange={(e) => setSignupForm({ ...signupForm, password: e.target.value })}
                            className="input-focus-ring bg-white/50"
                            required
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <label className="text-sm font-medium leading-none flex items-center gap-2">
                            <Lock className="w-4 h-4" />
                            Confirm Password
                          </label>
                          <Input
                            type="password"
                            placeholder="Confirm your password"
                            value={signupForm.confirmPassword}
                            onChange={(e) => setSignupForm({ ...signupForm, confirmPassword: e.target.value })}
                            className="input-focus-ring bg-white/50"
                            required
                          />
                        </div>
                        
                        <Button 
                          type="submit" 
                          className="btn-primary w-full"
                          disabled={loading}
                        >
                          {loading ? 'Creating Account...' : 'Create Account'}
                        </Button>
                      </form>
                    </TabsContent>
                  </Tabs>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowResetForm(false)}
                        className="p-1"
                      >
                        <ArrowLeft className="w-4 h-4" />
                      </Button>
                      <h3 className="text-lg font-semibold">Reset Password</h3>
                    </div>

                    <form onSubmit={handleResetPassword} className="space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium leading-none flex items-center gap-2">
                          <KeyRound className="w-4 h-4" />
                          Email Address
                        </label>
                        <Input
                          type="email"
                          placeholder="Enter your email"
                          value={resetForm.email}
                          onChange={(e) => setResetForm({ ...resetForm, email: e.target.value })}
                          className="input-focus-ring bg-white/50"
                          required
                        />
                      </div>
                      
                      <Button 
                        type="submit" 
                        className="btn-primary w-full"
                        disabled={loading}
                      >
                        {loading ? 'Sending...' : 'Send Reset Email'}
                      </Button>
                    </form>
                    
                    <p className="text-xs text-gray-600 text-center">
                      We'll send you a link to reset your password
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Auth;

