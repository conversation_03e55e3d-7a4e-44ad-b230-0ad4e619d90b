import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import BackgroundEffects from '@/components/BackgroundEffects';
import Logo from '@/components/Logo';
import { StripeService, SubscriptionWithPlan } from '@/lib/stripe';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { Link } from 'react-router-dom';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  Check,
  Crown,
  Zap,
  Users,
  ArrowLeft,
  Loader2,
  FileText,
  ExternalLink,
  User,
  LogOut,
  Folder<PERSON><PERSON>,
  BarChart3
} from 'lucide-react';
import { motion } from 'framer-motion';
import Header from '@/components/Header';

const Pricing: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAuthenticated, user, profile, signOut } = useAuth();
  const [plans, setPlans] = useState<any[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<SubscriptionWithPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  useEffect(() => {
    loadPlans();
    if (isAuthenticated) {
      loadCurrentSubscription();
    }

    // Check for success/cancel params
    if (searchParams.get('success') === 'true') {
      toast.success('Subscription activated successfully!');
    } else if (searchParams.get('canceled') === 'true') {
      toast.info('Subscription canceled. You can try again anytime.');
    }
  }, [isAuthenticated, searchParams]);

  const loadPlans = async () => {
    try {
      const { plans: plansData, error } = await StripeService.getSubscriptionPlans();
      if (error) {
        toast.error('Failed to load pricing plans');
        return;
      }
      // Filter to show only Premium plan ($7/monthly)
      const premiumPlan = plansData.find(plan => plan.name.toLowerCase() === 'premium');
      setPlans(premiumPlan ? [premiumPlan] : []);
    } catch (error) {
      console.error('Error loading plans:', error);
      toast.error('Failed to load pricing plans');
    } finally {
      setLoading(false);
    }
  };

  const loadCurrentSubscription = async () => {
    try {
      const { subscription } = await StripeService.getUserSubscription();
      setCurrentSubscription(subscription);
    } catch (error) {
      console.error('Error loading subscription:', error);
    }
  };

  const handleSubscribe = async (planId: string) => {
    if (!isAuthenticated) {
      navigate('/auth');
      return;
    }

    setProcessingPlan(planId);
    try {
      const { url, error } = await StripeService.createCheckoutSession({
        planId,
        billingCycle,
        successUrl: `${window.location.origin}/pricing?success=true`,
        cancelUrl: `${window.location.origin}/pricing?canceled=true`,
      });

      if (error) {
        toast.error(error.message);
        return;
      }

      if (url) {
        window.location.href = url;
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Failed to start checkout process');
    } finally {
      setProcessingPlan(null);
    }
  };

  const getPlanFeatures = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'free':
        return [
          '50 prompts per month',
          'Basic prompt styles',
          'Save up to 10 projects',
          'Community support',
          'Basic templates'
        ];
      case 'premium':
        return [
          'Unlimited prompts',
          'All premium styles',
          'Unlimited projects',
          'Priority support',
          'Premium templates',
          'Advanced features',
          'Export capabilities'
        ];
      case 'master':
        return [
          'Everything in Premium',
          'Create template collections',
          'Share with team members',
          'Advanced analytics',
          'Custom branding',
          'API access',
          'White-label options'
        ];
      default:
        return [];
    }
  };

  const getPlanIcon = (planName: string) => {
    switch (planName.toLowerCase()) {
      case 'free':
        return <Zap className="w-6 h-6" />;
      case 'premium':
        return <Crown className="w-6 h-6" />;
      case 'master':
        return <Users className="w-6 h-6" />;
      default:
        return <Zap className="w-6 h-6" />;
    }
  };

  const isCurrentPlan = (planId: string) => {
    return currentSubscription?.plan_id === planId;
  };

  if (loading) {
    return (
      <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
        <BackgroundEffects />
        <div className="relative z-10">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundEffects />
      
      <div className="relative z-10">
        <Header />
        <main className="container max-w-2xl mx-auto px-6 py-12">
          <div className="text-center mb-12">
            <h1 className="text-5xl font-bold tracking-tight mb-4">
              Unlock <span className="text-highlight">Premium Access</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Get unlimited access to AI-powered prompt generation for just $7/month
            </p>
       
          </div>

          <div className="flex justify-center">
            {plans.map((plan, index) => {
              const features = getPlanFeatures(plan.name);
              const isCurrent = isCurrentPlan(plan.id);

              return (
                <motion.div
                  key={plan.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="relative max-w-md w-full"
                >
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                      Premium Access
                    </Badge>
                  </div>

                  <Card className={`h-full border-blue-500 shadow-lg ${isCurrent ? 'ring-2 ring-green-500' : ''}`}>
                    <CardHeader className="text-center pb-8">
                      <div className="flex justify-center mb-4">
                        <Crown className="w-8 h-8 text-blue-600" />
                      </div>
                      <CardTitle className="text-3xl">Premium</CardTitle>
                      <CardDescription className="text-base">
                        Unlimited AI-powered prompt generation
                      </CardDescription>
                      <div className="mt-6">
                        <div className="text-5xl font-bold text-blue-600">
                          $7
                          <span className="text-xl font-normal text-gray-600">/month</span>
                        </div>
                        <p className="text-sm text-gray-500 mt-2">
                          Simple, transparent pricing
                        </p>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-6">
                      <ul className="space-y-4">
                        {features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center gap-3">
                            <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                            <span className="text-base">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      <Button
                        className="w-full h-12 text-lg"
                        size="lg"
                        onClick={() => handleSubscribe(plan.id)}
                        disabled={processingPlan === plan.id || isCurrent}
                      >
                        {processingPlan === plan.id ? (
                          <>
                            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : isCurrent ? (
                          'Current Plan'
                        ) : (
                          'Subscribe Now'
                        )}
                      </Button>

                      {isCurrent && (
                        <p className="text-center text-sm text-green-600 font-medium">
                          ✓ You're currently subscribed
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </div>

          <div className="text-center mt-12">
            <div className="bg-gray-50 rounded-lg p-6 mb-8">
              <h3 className="text-lg font-semibold mb-2">Why Choose Premium?</h3>
              <p className="text-gray-600">
                Join thousands of users who have transformed their AI interactions with unlimited access to professional prompt generation.
              </p>
            </div>

            <p className="text-gray-600 mb-4">
              Questions? <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">Contact our support team</a>
            </p>
            <p className="text-sm text-gray-500">
              Secure payment processing by Stripe. Cancel anytime.
            </p>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Pricing;
