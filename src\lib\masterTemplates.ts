import { supabase, MasterTemplate, MasterTemplateInsert, MasterTemplateUpdate } from './supabase';
import { toast } from 'sonner';

export interface CreateMasterTemplateData {
  title: string;
  description?: string;
  category: string;
  userIdea: string;
  selectedStyle: string;
  generatedPrompt: string;
  requiredPlan?: 'free' | 'premium' | 'admin';
}

export interface MasterTemplateFilters {
  search?: string;
  category?: string;
  requiredPlan?: string;
  sortBy?: 'created_at' | 'updated_at' | 'title' | 'usage_count';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

export class MasterTemplateService {
  /**
   * Create a new master template (admin only)
   */
  static async createMasterTemplate(data: CreateMasterTemplateData): Promise<{ template: MasterTemplate | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { template: null, error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { template: null, error: new Error('Admin access required') };
      }

      const templateContent = {
        userIdea: data.userIdea,
        selectedStyle: data.selectedStyle,
        generatedPrompt: data.generatedPrompt,
        metadata: {
          wordCount: data.generatedPrompt.split(' ').length,
          characterCount: data.generatedPrompt.length,
          tags: [],
        }
      };

      const templateData: MasterTemplateInsert = {
        created_by: user.id,
        title: data.title,
        description: data.description || null,
        category: data.category,
        content: templateContent,
        required_plan: data.requiredPlan || 'free',
        is_active: true,
        usage_count: 0,
      };

      const { data: template, error } = await supabase
        .from('master_templates')
        .insert(templateData)
        .select()
        .single();

      if (error) {
        console.error('Error creating master template:', error);
        return { template: null, error: new Error(error.message) };
      }

      return { template, error: null };
    } catch (error) {
      console.error('Error creating master template:', error);
      return { template: null, error: error as Error };
    }
  }

  /**
   * Get master templates available to user based on their subscription
   */
  static async getMasterTemplates(filters: MasterTemplateFilters = {}): Promise<{ templates: MasterTemplate[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { templates: [], error: new Error('User not authenticated') };
      }

      let query = supabase
        .from('master_templates')
        .select('*')
        .eq('is_active', true);

      // Apply filters
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      if (filters.category) {
        query = query.eq('category', filters.category);
      }

      if (filters.requiredPlan) {
        query = query.eq('required_plan', filters.requiredPlan);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
      }

      const { data: templates, error } = await query;

      if (error) {
        console.error('Error fetching master templates:', error);
        return { templates: [], error: new Error(error.message) };
      }

      return { templates: templates || [], error: null };
    } catch (error) {
      console.error('Error fetching master templates:', error);
      return { templates: [], error: error as Error };
    }
  }

  /**
   * Get a single master template by ID
   */
  static async getMasterTemplate(templateId: string): Promise<{ template: MasterTemplate | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { template: null, error: new Error('User not authenticated') };
      }

      const { data: template, error } = await supabase
        .from('master_templates')
        .select('*')
        .eq('id', templateId)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error fetching master template:', error);
        return { template: null, error: new Error(error.message) };
      }

      return { template, error: null };
    } catch (error) {
      console.error('Error fetching master template:', error);
      return { template: null, error: error as Error };
    }
  }

  /**
   * Update a master template (admin only)
   */
  static async updateMasterTemplate(templateId: string, updates: Partial<MasterTemplateUpdate>): Promise<{ template: MasterTemplate | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { template: null, error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { template: null, error: new Error('Admin access required') };
      }

      const { data: template, error } = await supabase
        .from('master_templates')
        .update(updates)
        .eq('id', templateId)
        .select()
        .single();

      if (error) {
        console.error('Error updating master template:', error);
        return { template: null, error: new Error(error.message) };
      }

      return { template, error: null };
    } catch (error) {
      console.error('Error updating master template:', error);
      return { template: null, error: error as Error };
    }
  }

  /**
   * Delete a master template (admin only)
   */
  static async deleteMasterTemplate(templateId: string): Promise<{ success: boolean; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { success: false, error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { success: false, error: new Error('Admin access required') };
      }

      const { error } = await supabase
        .from('master_templates')
        .delete()
        .eq('id', templateId);

      if (error) {
        console.error('Error deleting master template:', error);
        return { success: false, error: new Error(error.message) };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Error deleting master template:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * Use a master template (increment usage count)
   */
  static async useMasterTemplate(templateId: string): Promise<{ template: MasterTemplate | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { template: null, error: new Error('User not authenticated') };
      }

      // Get the template first
      const { template, error: fetchError } = await this.getMasterTemplate(templateId);
      
      if (fetchError || !template) {
        return { template: null, error: fetchError || new Error('Template not found') };
      }

      // Increment usage count
      const { data: updatedTemplate, error: updateError } = await supabase
        .from('master_templates')
        .update({ usage_count: template.usage_count + 1 })
        .eq('id', templateId)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating template usage:', updateError);
        // Don't fail the operation if usage count update fails
      }

      return { template: updatedTemplate || template, error: null };
    } catch (error) {
      console.error('Error using master template:', error);
      return { template: null, error: error as Error };
    }
  }

  /**
   * Get template categories
   */
  static async getTemplateCategories(): Promise<{ categories: string[]; error: Error | null }> {
    try {
      const { data: templates, error } = await supabase
        .from('master_templates')
        .select('category')
        .eq('is_active', true);

      if (error) {
        console.error('Error fetching template categories:', error);
        return { categories: [], error: new Error(error.message) };
      }

      // Get unique categories
      const categories = [...new Set(templates?.map(t => t.category) || [])];
      
      return { categories, error: null };
    } catch (error) {
      console.error('Error fetching template categories:', error);
      return { categories: [], error: error as Error };
    }
  }

  /**
   * Update a master template (admin only)
   */
  static async updateMasterTemplate(templateId: string, updates: Partial<MasterTemplateUpdate>): Promise<{ template: MasterTemplate | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return { template: null, error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { template: null, error: new Error('Admin access required') };
      }

      const { data: template, error } = await supabase
        .from('master_templates')
        .update(updates)
        .eq('id', templateId)
        .select()
        .single();

      if (error) {
        console.error('Error updating master template:', error);
        return { template: null, error: new Error(error.message) };
      }

      return { template, error: null };
    } catch (error) {
      console.error('Error updating master template:', error);
      return { template: null, error: error as Error };
    }
  }

  /**
   * Delete a master template (admin only)
   */
  static async deleteMasterTemplate(templateId: string): Promise<{ success: boolean; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return { success: false, error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { success: false, error: new Error('Admin access required') };
      }

      const { error } = await supabase
        .from('master_templates')
        .delete()
        .eq('id', templateId);

      if (error) {
        console.error('Error deleting master template:', error);
        return { success: false, error: new Error(error.message) };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Error deleting master template:', error);
      return { success: false, error: error as Error };
    }
  }
}
