-- Comprehensive Tagging and Categorization System
-- This migration adds full support for tags, categories, and hashtags

-- Add tags and categories support back to projects table
ALTER TABLE public.projects 
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS category TEXT,
ADD COLUMN IF NOT EXISTS custom_tags JSONB DEFAULT '[]';

-- Create tags table for managing all available tags
CREATE TABLE IF NOT EXISTS public.tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    color TEXT DEFAULT '#3B82F6', -- Default blue color
    description TEXT,
    usage_count INTEGER DEFAULT 0,
    is_system_tag BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create categories table for managing categories
CREATE TABLE IF NOT EXISTS public.categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    color TEXT DEFAULT '#10B981', -- Default green color
    icon TEXT, -- Icon name or emoji
    usage_count INTEGER DEFAULT 0,
    is_system_category BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create project_tags junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS public.project_tags (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    tag_id UUID REFERENCES public.tags(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(project_id, tag_id)
);

-- Create project_categories junction table (projects can have multiple categories)
CREATE TABLE IF NOT EXISTS public.project_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.categories(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(project_id, category_id)
);

-- Insert default system categories
INSERT INTO public.categories (name, description, color, icon, is_system_category, usage_count) VALUES
('Marketing', 'Marketing and advertising related prompts', '#F59E0B', '📢', TRUE, 0),
('Technical', 'Technical and programming related prompts', '#3B82F6', '⚙️', TRUE, 0),
('Creative Writing', 'Creative writing and storytelling prompts', '#8B5CF6', '✍️', TRUE, 0),
('Business', 'Business and professional prompts', '#10B981', '💼', TRUE, 0),
('Education', 'Educational and learning prompts', '#F97316', '🎓', TRUE, 0),
('Research', 'Research and analysis prompts', '#06B6D4', '🔬', TRUE, 0),
('Personal', 'Personal and lifestyle prompts', '#EC4899', '👤', TRUE, 0),
('Social Media', 'Social media content and strategy', '#8B5CF6', '📱', TRUE, 0),
('Email Marketing', 'Email campaigns and newsletters', '#F59E0B', '📧', TRUE, 0),
('SEO', 'Search engine optimization content', '#10B981', '🔍', TRUE, 0),
('E-commerce', 'Online store and product descriptions', '#3B82F6', '🛒', TRUE, 0),
('Real Estate', 'Property and real estate content', '#F97316', '🏠', TRUE, 0)
ON CONFLICT (name) DO NOTHING;

-- Insert default system tags
INSERT INTO public.tags (name, color, description, is_system_tag, usage_count) VALUES
('urgent', '#EF4444', 'High priority or time-sensitive prompts', TRUE, 0),
('draft', '#6B7280', 'Work in progress or draft prompts', TRUE, 0),
('template', '#8B5CF6', 'Reusable template prompts', TRUE, 0),
('favorite', '#F59E0B', 'Favorite or bookmarked prompts', TRUE, 0),
('public', '#10B981', 'Publicly shared prompts', TRUE, 0),
('private', '#EF4444', 'Private or confidential prompts', TRUE, 0),
('ai-generated', '#3B82F6', 'Generated by AI assistance', TRUE, 0),
('human-written', '#10B981', 'Written entirely by humans', TRUE, 0),
('collaborative', '#8B5CF6', 'Created through collaboration', TRUE, 0),
('experimental', '#F97316', 'Experimental or testing prompts', TRUE, 0)
ON CONFLICT (name) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_projects_tags ON public.projects USING GIN (tags);
CREATE INDEX IF NOT EXISTS idx_projects_category ON public.projects(category);
CREATE INDEX IF NOT EXISTS idx_projects_custom_tags ON public.projects USING GIN (custom_tags);

CREATE INDEX IF NOT EXISTS idx_tags_name ON public.tags(name);
CREATE INDEX IF NOT EXISTS idx_tags_usage_count ON public.tags(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_tags_created_by ON public.tags(created_by);

CREATE INDEX IF NOT EXISTS idx_categories_name ON public.categories(name);
CREATE INDEX IF NOT EXISTS idx_categories_usage_count ON public.categories(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_categories_created_by ON public.categories(created_by);

CREATE INDEX IF NOT EXISTS idx_project_tags_project_id ON public.project_tags(project_id);
CREATE INDEX IF NOT EXISTS idx_project_tags_tag_id ON public.project_tags(tag_id);

CREATE INDEX IF NOT EXISTS idx_project_categories_project_id ON public.project_categories(project_id);
CREATE INDEX IF NOT EXISTS idx_project_categories_category_id ON public.project_categories(category_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_categories ENABLE ROW LEVEL SECURITY;

-- RLS Policies for tags
CREATE POLICY "Users can view all tags" ON public.tags
    FOR SELECT USING (TRUE);

CREATE POLICY "Users can create their own tags" ON public.tags
    FOR INSERT WITH CHECK (auth.uid() = created_by OR created_by IS NULL);

CREATE POLICY "Users can update their own tags" ON public.tags
    FOR UPDATE USING (auth.uid() = created_by OR is_system_tag = FALSE);

CREATE POLICY "Users can delete their own tags" ON public.tags
    FOR DELETE USING (auth.uid() = created_by AND is_system_tag = FALSE);

-- RLS Policies for categories
CREATE POLICY "Users can view all categories" ON public.categories
    FOR SELECT USING (TRUE);

CREATE POLICY "Users can create their own categories" ON public.categories
    FOR INSERT WITH CHECK (auth.uid() = created_by OR created_by IS NULL);

CREATE POLICY "Users can update their own categories" ON public.categories
    FOR UPDATE USING (auth.uid() = created_by OR is_system_category = FALSE);

CREATE POLICY "Users can delete their own categories" ON public.categories
    FOR DELETE USING (auth.uid() = created_by AND is_system_category = FALSE);

-- RLS Policies for project_tags
CREATE POLICY "Users can view project tags for accessible projects" ON public.project_tags
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.projects p 
            WHERE p.id = project_id 
            AND (p.user_id = auth.uid() OR p.is_public = TRUE)
        )
    );

CREATE POLICY "Users can manage tags for their own projects" ON public.project_tags
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects p 
            WHERE p.id = project_id 
            AND p.user_id = auth.uid()
        )
    );

-- RLS Policies for project_categories
CREATE POLICY "Users can view project categories for accessible projects" ON public.project_categories
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.projects p 
            WHERE p.id = project_id 
            AND (p.user_id = auth.uid() OR p.is_public = TRUE)
        )
    );

CREATE POLICY "Users can manage categories for their own projects" ON public.project_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.projects p
            WHERE p.id = project_id
            AND p.user_id = auth.uid()
        )
    );

-- Functions to maintain usage counts
CREATE OR REPLACE FUNCTION update_tag_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.tags
        SET usage_count = usage_count + 1, updated_at = NOW()
        WHERE id = NEW.tag_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.tags
        SET usage_count = GREATEST(usage_count - 1, 0), updated_at = NOW()
        WHERE id = OLD.tag_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION update_category_usage_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.categories
        SET usage_count = usage_count + 1, updated_at = NOW()
        WHERE id = NEW.category_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.categories
        SET usage_count = GREATEST(usage_count - 1, 0), updated_at = NOW()
        WHERE id = OLD.category_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to extract hashtags from text
CREATE OR REPLACE FUNCTION extract_hashtags(input_text TEXT)
RETURNS TEXT[] AS $$
DECLARE
    hashtags TEXT[];
BEGIN
    -- Extract hashtags using regex (matches #word, #word123, etc.)
    SELECT array_agg(DISTINCT LOWER(substring(match FROM 2)))
    INTO hashtags
    FROM regexp_split_to_table(input_text, '\s+') AS match
    WHERE match ~ '^#[a-zA-Z0-9_]+$';

    RETURN COALESCE(hashtags, '{}');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to auto-create tags from hashtags
CREATE OR REPLACE FUNCTION auto_create_tags_from_hashtags(hashtag_names TEXT[], user_id UUID DEFAULT NULL)
RETURNS UUID[] AS $$
DECLARE
    tag_ids UUID[] := '{}';
    hashtag_name TEXT;
    tag_id UUID;
BEGIN
    FOREACH hashtag_name IN ARRAY hashtag_names
    LOOP
        -- Try to find existing tag
        SELECT id INTO tag_id FROM public.tags WHERE name = hashtag_name;

        -- If tag doesn't exist, create it
        IF tag_id IS NULL THEN
            INSERT INTO public.tags (name, created_by, is_system_tag)
            VALUES (hashtag_name, user_id, FALSE)
            RETURNING id INTO tag_id;
        END IF;

        tag_ids := array_append(tag_ids, tag_id);
    END LOOP;

    RETURN tag_ids;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers for usage count maintenance
CREATE TRIGGER on_project_tag_change
    AFTER INSERT OR DELETE ON public.project_tags
    FOR EACH ROW EXECUTE FUNCTION update_tag_usage_count();

CREATE TRIGGER on_project_category_change
    AFTER INSERT OR DELETE ON public.project_categories
    FOR EACH ROW EXECUTE FUNCTION update_category_usage_count();
