import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Search, FileText, Crown, Copy, Play, TrendingUp, Lock } from 'lucide-react';
import { toast } from 'sonner';
import { MasterTemplateService } from '@/lib/masterTemplates';
import { MasterTemplate } from '@/lib/supabase';
import { usePremiumFeatures } from '@/hooks/useSubscription';
import { useNavigate } from 'react-router-dom';

interface MasterTemplatesBrowserProps {
  className?: string;
  onTemplateSelect?: (template: MasterTemplate) => void;
}

const MasterTemplatesBrowser: React.FC<MasterTemplatesBrowserProps> = ({ 
  className, 
  onTemplateSelect 
}) => {
  const [templates, setTemplates] = useState<MasterTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<MasterTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<MasterTemplate | null>(null);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const { hasPremiumAccess, hasAdminAccess } = usePremiumFeatures();
  const navigate = useNavigate();

  useEffect(() => {
    loadTemplates();
  }, []);

  // Client-side filtering
  useEffect(() => {
    let filtered = templates;

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(template => 
        template.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategory && selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    setFilteredTemplates(filtered);
  }, [templates, searchTerm, selectedCategory]);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const { templates: templatesData, error } = await MasterTemplateService.getMasterTemplates({
        limit: 100,
      });

      if (error) {
        toast.error('Failed to load templates');
        console.error('Error loading templates:', error);
      } else {
        setTemplates(templatesData);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const canAccessTemplate = (template: MasterTemplate) => {
    if (template.required_plan === 'free') return true;
    if (template.required_plan === 'premium') return hasPremiumAccess;
    if (template.required_plan === 'admin') return hasAdminAccess;
    return false;
  };

  const handleUseTemplate = async (template: MasterTemplate) => {
    if (!canAccessTemplate(template)) {
      toast.error('This template requires a premium subscription');
      return;
    }

    try {
      // Increment usage count
      await MasterTemplateService.useMasterTemplate(template.id);
      
      if (onTemplateSelect) {
        onTemplateSelect(template);
      } else {
        // Navigate to main page with template data
        const searchParams = new URLSearchParams({
          templateId: template.id,
          userIdea: template.content?.userIdea || '',
          selectedStyle: template.content?.selectedStyle || '',
          loadTemplate: 'true'
        });
        navigate(`/?${searchParams.toString()}`);
      }
      
      toast.success('Template loaded successfully');
    } catch (error) {
      console.error('Error using template:', error);
      toast.error('Failed to load template');
    }
  };

  const handleCopyPrompt = async (prompt: string) => {
    await navigator.clipboard.writeText(prompt);
    toast.success('Prompt copied to clipboard');
  };

  const handlePreview = (template: MasterTemplate) => {
    setSelectedTemplate(template);
    setShowPreviewDialog(true);
  };

  const getPlanBadge = (plan: string) => {
    const variants = {
      free: 'secondary',
      premium: 'default',
      admin: 'destructive',
    } as const;

    const icons = {
      free: null,
      premium: <Crown className="h-3 w-3 mr-1" />,
      admin: <Lock className="h-3 w-3 mr-1" />,
    };

    return (
      <Badge variant={variants[plan as keyof typeof variants] || 'secondary'} className="flex items-center">
        {icons[plan as keyof typeof icons]}
        {plan}
      </Badge>
    );
  };

  const uniqueCategories = Array.from(new Set(templates.map(t => t.category).filter(Boolean)));

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Master Templates
          </CardTitle>
          <CardDescription>
            Browse and use premium template collections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Controls */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {uniqueCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Templates Grid */}
          <div className="space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground mt-2">Loading templates...</p>
              </div>
            ) : filteredTemplates.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No templates found</p>
                <p className="text-sm text-muted-foreground">
                  {searchTerm || selectedCategory !== 'all' 
                    ? 'Try adjusting your filters' 
                    : 'No templates are available at the moment'
                  }
                </p>
              </div>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredTemplates.map((template) => {
                  const hasAccess = canAccessTemplate(template);
                  return (
                    <Card key={template.id} className={`hover:shadow-md transition-shadow ${!hasAccess ? 'opacity-75' : ''}`}>
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <h4 className="font-medium truncate">{template.title}</h4>
                              {template.description && (
                                <p className="text-sm text-muted-foreground line-clamp-2 mt-1">
                                  {template.description}
                                </p>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs">
                                {template.category}
                              </Badge>
                              {getPlanBadge(template.required_plan)}
                            </div>
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <TrendingUp className="h-3 w-3" />
                              <span>{template.usage_count}</span>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 pt-2 border-t">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handlePreview(template)}
                              className="flex-1"
                            >
                              Preview
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => handleUseTemplate(template)}
                              disabled={!hasAccess}
                              className="flex-1"
                            >
                              <Play className="h-3 w-3 mr-1" />
                              {hasAccess ? 'Use' : 'Locked'}
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>

          {/* Upgrade prompt for non-premium users */}
          {!hasPremiumAccess && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-yellow-800">
                  <Crown className="h-5 w-5" />
                  Unlock Premium Templates
                </CardTitle>
                <CardDescription className="text-yellow-700">
                  Get access to all premium templates and advanced features
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => navigate('/pricing')}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  Upgrade to Premium
                </Button>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Preview Dialog */}
      <Dialog open={showPreviewDialog} onOpenChange={setShowPreviewDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedTemplate?.title}
              {selectedTemplate && getPlanBadge(selectedTemplate.required_plan)}
            </DialogTitle>
            <DialogDescription>
              {selectedTemplate?.description}
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Category</h4>
                <Badge variant="outline">{selectedTemplate.category}</Badge>
              </div>
              {selectedTemplate.content?.userIdea && (
                <div>
                  <h4 className="font-medium mb-2">User Idea</h4>
                  <p className="text-sm text-muted-foreground">{selectedTemplate.content.userIdea}</p>
                </div>
              )}
              {selectedTemplate.content?.selectedStyle && (
                <div>
                  <h4 className="font-medium mb-2">Style</h4>
                  <Badge variant="secondary">{selectedTemplate.content.selectedStyle}</Badge>
                </div>
              )}
              <div>
                <h4 className="font-medium mb-2">Generated Prompt</h4>
                <div className="bg-gray-50 p-3 rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{selectedTemplate.content?.generatedPrompt}</p>
                </div>
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <TrendingUp className="h-4 w-4" />
                <span>{selectedTemplate.usage_count} uses</span>
                <span>•</span>
                <span>Created {new Date(selectedTemplate.created_at).toLocaleDateString()}</span>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPreviewDialog(false)}>
              Close
            </Button>
            {selectedTemplate && (
              <>
                <Button
                  variant="outline"
                  onClick={() => handleCopyPrompt(selectedTemplate.content?.generatedPrompt || '')}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Prompt
                </Button>
                <Button
                  onClick={() => {
                    handleUseTemplate(selectedTemplate);
                    setShowPreviewDialog(false);
                  }}
                  disabled={!canAccessTemplate(selectedTemplate)}
                >
                  <Play className="h-4 w-4 mr-2" />
                  {canAccessTemplate(selectedTemplate) ? 'Use Template' : 'Requires Premium'}
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MasterTemplatesBrowser;
