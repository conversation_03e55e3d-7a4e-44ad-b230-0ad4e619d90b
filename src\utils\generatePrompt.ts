
import { toast } from "sonner";
import { promptStyles } from "./promptTemplates";
import { generateWithGroq } from "@/lib/groqApi";
import { isGroqConfigured } from "@/lib/groq";

export const generatePrompt = async (idea: string, styleId: string): Promise<string> => {
  if (!idea.trim()) {
    toast.error("Please enter an idea first");
    return "";
  }

  const selectedStyle = promptStyles.find(style => style.id === styleId);
  
  if (!selectedStyle) {
    toast.error("Invalid prompt style selected");
    return "";
  }

  try {
    // Check if GROQ is configured
    if (isGroqConfigured()) {
      // Use GROQ API
      return await generateWithGroq(idea, selectedStyle.name);
    } else {
      // Fallback to template-based generation
      return selectedStyle.template(idea);
    }
  } catch (error) {
    console.error("Error generating prompt:", error);
    toast.error("Failed to generate prompt");
    return "";
  }
};
