import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { ChatService } from '@/lib/chatService';
import { Project, Chat } from '@/lib/supabase';
import { 
  Plus, 
  Search, 
  MessageSquare, 
  Folder, 
  FolderOpen, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  ChevronRight, 
  ChevronDown,
  PanelLeftClose,
  PanelLeft,
  Settings,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface SidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  currentChatId?: string;
  onChatSelect: (chatId: string) => void;
  onNewChat: () => void;
  onNewProject: () => void;
}

interface ProjectWithChats extends Project {
  chats: Chat[];
}

const ChatSidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  onToggleCollapse,
  currentChatId,
  onChatSelect,
  onNewChat,
  onNewProject
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, signOut } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [projects, setProjects] = useState<ProjectWithChats[]>([]);
  const [standaloneChats, setStandaloneChats] = useState<Chat[]>([]);
  const [expandedProjects, setExpandedProjects] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load projects and chats from API
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load projects with their chats
      const { projects: projectsData, error: projectsError } = await ChatService.getUserProjects();

      if (projectsError) {
        setError('Failed to load projects');
        console.error('Error loading projects:', projectsError);
      } else {
        setProjects(projectsData as ProjectWithChats[]);
        // Expand projects that have chats by default
        const projectsWithChats = projectsData.filter(p => (p as any).chats?.length > 0);
        setExpandedProjects(new Set(projectsWithChats.map(p => p.id)));
      }

      // Load standalone chats
      const { chats: chatsData, error: chatsError } = await ChatService.getStandaloneChats();

      if (chatsError) {
        console.error('Error loading standalone chats:', chatsError);
      } else {
        setStandaloneChats(chatsData);
      }

    } catch (error) {
      console.error('Error loading sidebar data:', error);
      setError('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const toggleProjectExpansion = (projectId: string) => {
    setExpandedProjects(prev => {
      const newSet = new Set(prev);
      if (newSet.has(projectId)) {
        newSet.delete(projectId);
      } else {
        newSet.add(projectId);
      }
      return newSet;
    });
  };

  const handleChatClick = (chatId: string) => {
    onChatSelect(chatId);
  };

  const handleProjectSettings = (projectId: string) => {
    navigate(`/projects/${projectId}`);
  };

  const handleDeleteProject = async (projectId: string) => {
    if (!window.confirm('Are you sure you want to delete this project and all its chats?')) {
      return;
    }

    try {
      const { error } = await ChatService.deleteProject(projectId);
      if (error) {
        console.error('Error deleting project:', error);
        return;
      }

      // Refresh data
      loadData();
    } catch (error) {
      console.error('Error deleting project:', error);
    }
  };

  const handleDeleteChat = async (chatId: string) => {
    if (!window.confirm('Are you sure you want to delete this chat?')) {
      return;
    }

    try {
      const { error } = await ChatService.deleteChat(chatId);
      if (error) {
        console.error('Error deleting chat:', error);
        return;
      }

      // Refresh data
      loadData();
    } catch (error) {
      console.error('Error deleting chat:', error);
    }
  };

  const handleRenameChat = async (chatId: string, newTitle: string) => {
    try {
      const { error } = await ChatService.updateChatTitle(chatId, newTitle);
      if (error) {
        console.error('Error renaming chat:', error);
        return;
      }

      // Refresh data
      loadData();
    } catch (error) {
      console.error('Error renaming chat:', error);
    }
  };

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (project as any).chats?.some((chat: Chat) => chat.title.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const filteredStandaloneChats = standaloneChats.filter(chat =>
    chat.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  if (isCollapsed) {
    return (
      <div className="fixed left-0 top-0 w-12 h-screen bg-gray-50 border-r border-gray-200 flex flex-col items-center py-4 z-40 transition-all duration-200 ease-linear">
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleCollapse}
          className="mb-4"
        >
          <PanelLeft className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={onNewChat}
          className="mb-2"
        >
          <Plus className="w-4 h-4" />
        </Button>

        <div className="flex-1" />

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <User className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" side="right">
            <DropdownMenuItem onClick={() => navigate('/settings')}>
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={signOut}>
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }

  return (
    <div className="fixed left-0 top-0 w-64 h-screen bg-gray-50 border-r border-gray-200 flex flex-col z-40 transition-all duration-200 ease-linear">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="font-semibold text-gray-900">ChatsPrompt</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleCollapse}
          >
            <PanelLeftClose className="w-4 h-4" />
          </Button>
        </div>

        {/* New Chat/Project Buttons */}
        <div className="space-y-2">
          <Button
            onClick={onNewChat}
            className="w-full justify-start"
            variant="outline"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Chat
          </Button>
          
          <Button
            onClick={onNewProject}
            className="w-full justify-start"
            variant="outline"
          >
            <Folder className="w-4 h-4 mr-2" />
            New Project
          </Button>
        </div>

        {/* Search */}
        <div className="relative mt-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search chats..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Chat List */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {loading ? (
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-10 bg-gray-200 rounded-lg mb-2"></div>
                  <div className="ml-6 space-y-1">
                    <div className="h-8 bg-gray-100 rounded"></div>
                    <div className="h-8 bg-gray-100 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <p className="text-sm">{error}</p>
              <Button variant="outline" size="sm" onClick={loadData} className="mt-2">
                Retry
              </Button>
            </div>
          ) : (
            <>
              {/* Projects */}
              {filteredProjects.map((project) => {
                const projectChats = (project as any).chats || [];
                return (
                  <div key={project.id} className="mb-2">
                    <Collapsible
                      open={expandedProjects.has(project.id)}
                      onOpenChange={() => toggleProjectExpansion(project.id)}
                    >
                      <div className="flex items-center group">
                        <CollapsibleTrigger asChild>
                          <Button
                            variant="ghost"
                            className="flex-1 justify-start p-2 h-auto"
                          >
                            {expandedProjects.has(project.id) ? (
                              <ChevronDown className="w-4 h-4 mr-1" />
                            ) : (
                              <ChevronRight className="w-4 h-4 mr-1" />
                            )}
                            {expandedProjects.has(project.id) ? (
                              <FolderOpen className="w-4 h-4 mr-2" />
                            ) : (
                              <Folder className="w-4 h-4 mr-2" />
                            )}
                            <span className="truncate">{project.name}</span>
                            <Badge variant="secondary" className="ml-auto text-xs">
                              {projectChats.length}
                            </Badge>
                          </Button>
                        </CollapsibleTrigger>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleProjectSettings(project.id)}>
                              <Settings className="w-4 h-4 mr-2" />
                              Settings
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDeleteProject(project.id)}>
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <CollapsibleContent>
                        <AnimatePresence>
                          <div className="ml-6 space-y-1">
                            {projectChats.map((chat: Chat) => (
                              <motion.div
                                key={chat.id}
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                exit={{ opacity: 0, height: 0 }}
                                transition={{ duration: 0.2 }}
                              >
                                <ChatItem
                                  chat={chat}
                                  isActive={currentChatId === chat.id}
                                  onClick={() => handleChatClick(chat.id)}
                                  onDelete={() => handleDeleteChat(chat.id)}
                                  onRename={(newTitle) => handleRenameChat(chat.id, newTitle)}
                                  formatDate={formatDate}
                                />
                              </motion.div>
                            ))}
                          </div>
                        </AnimatePresence>
                      </CollapsibleContent>
                    </Collapsible>
                  </div>
                );
              })}
            </>
          )}

          {/* Standalone Chats */}
          {filteredStandaloneChats.length > 0 && (
            <>
              {filteredProjects.length > 0 && (
                <Separator className="my-4" />
              )}
              <div className="space-y-1">
                {filteredStandaloneChats.map((chat) => (
                  <ChatItem
                    key={chat.id}
                    chat={chat}
                    isActive={currentChatId === chat.id}
                    onClick={() => handleChatClick(chat.id)}
                    onDelete={() => handleDeleteChat(chat.id)}
                    onRename={(newTitle) => handleRenameChat(chat.id, newTitle)}
                    formatDate={formatDate}
                  />
                ))}
              </div>
            </>
          )}

          {/* Empty State */}
          {filteredProjects.length === 0 && filteredStandaloneChats.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No chats found</p>
              {searchQuery && (
                <p className="text-xs mt-1">Try a different search term</p>
              )}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-start">
              <User className="w-4 h-4 mr-2" />
              <span className="truncate">{user?.email}</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" side="top">
            <DropdownMenuItem onClick={() => navigate('/settings')}>
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={signOut}>
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

interface ChatItemProps {
  chat: Chat;
  isActive: boolean;
  onClick: () => void;
  onDelete: () => void;
  onRename: (newTitle: string) => void;
  formatDate: (dateString: string) => string;
}

const ChatItem: React.FC<ChatItemProps> = ({ chat, isActive, onClick, onDelete, onRename, formatDate }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isRenaming, setIsRenaming] = useState(false);
  const [newTitle, setNewTitle] = useState(chat.title);

  const handleRename = () => {
    if (newTitle.trim() && newTitle !== chat.title) {
      onRename(newTitle.trim());
    }
    setIsRenaming(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      setNewTitle(chat.title);
      setIsRenaming(false);
    }
  };

  return (
    <div
      className={cn(
        "group flex items-center p-2 rounded-lg cursor-pointer transition-colors",
        isActive ? "bg-blue-100 text-blue-900" : "hover:bg-gray-100"
      )}
      onClick={isRenaming ? undefined : onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <MessageSquare className="w-4 h-4 mr-2 flex-shrink-0" />
      <div className="flex-1 min-w-0">
        {isRenaming ? (
          <Input
            value={newTitle}
            onChange={(e) => setNewTitle(e.target.value)}
            onBlur={handleRename}
            onKeyDown={handleKeyPress}
            className="h-6 text-sm p-1"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <>
            <p className="text-sm font-medium truncate">{chat.title}</p>
            <div className="flex items-center justify-between">
              <p className="text-xs text-gray-500 truncate">
                {chat.last_message_preview || 'No messages yet'}
              </p>
              <span className="text-xs text-gray-400 ml-2">
                {formatDate(chat.updated_at)}
              </span>
            </div>
          </>
        )}
      </div>

      {(isHovered || isActive) && !isRenaming && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => e.stopPropagation()}
            >
              <MoreHorizontal className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsRenaming(true)}>
              <Edit className="w-4 h-4 mr-2" />
              Rename
            </DropdownMenuItem>
            <DropdownMenuItem onClick={onDelete}>
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
};

export default ChatSidebar;
