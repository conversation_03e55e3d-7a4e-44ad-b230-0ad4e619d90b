# SaaS Implementation Guide

This guide will help you set up the complete SaaS functionality for Promptology Galaxy, including payment processing, subscription management, and premium features.

## 🚀 Features Implemented

### ✅ **Core SaaS Infrastructure**
- User authentication & profiles
- Subscription plans (Free, Premium, Master)
- Usage tracking & limits
- Payment processing with Stripe
- Admin dashboard
- Master account template system

### ✅ **Frontend Components**
- Pricing page (`/pricing`)
- User dashboard (`/dashboard`)
- Enhanced admin panel (`/admin`)
- Usage tracking integration
- Subscription management UI

### ✅ **Backend Services**
- Stripe payment integration
- Webhook handling
- Usage limits enforcement
- Master template management

## 📋 Setup Instructions

### 1. Database Migration

Run the SaaS migration to add the necessary tables:

```bash
# Apply the migration
supabase db push

# Or if using migration files
supabase migration up
```

The migration adds:
- `subscription_plans` - Available subscription tiers
- `user_subscriptions` - User subscription records
- `user_usage` - Monthly usage tracking
- `master_templates` - Premium template collections
- `project_access` - Project sharing permissions
- `payment_history` - Payment records

### 2. Stripe Configuration

1. **Create a Stripe account** at https://stripe.com
2. **Get your API keys** from the Stripe dashboard
3. **Create subscription products** in Stripe:
   - Free Plan: $0/month
   - Premium Plan: $7/month
   - Master Plan: $19/month
4. **Set up webhooks** pointing to your API endpoint

### 3. Environment Variables

Update your `.env` file with the following variables:

```env
# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key
STRIPE_SECRET_KEY=sk_test_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Application URL
VITE_APP_URL=http://localhost:8080
```

### 4. Install Dependencies

The Stripe dependencies should already be installed. If not:

```bash
npm install stripe @stripe/stripe-js
```

### 5. Deploy Supabase Edge Functions

The payment processing uses Supabase Edge Functions:

1. **Deploy Edge Functions**:
   ```bash
   supabase functions deploy create-checkout-session
   supabase functions deploy create-portal-session
   supabase functions deploy cancel-subscription
   supabase functions deploy stripe-webhook
   ```

2. **Set environment variables** in Supabase dashboard:
   - `STRIPE_SECRET_KEY`
   - `STRIPE_WEBHOOK_SECRET`

3. **Configure Stripe webhooks** to point to:
   - `https://your-project-ref.supabase.co/functions/v1/stripe-webhook`

### 6. Update Stripe Price IDs

After creating products in Stripe, update the database:

```sql
UPDATE subscription_plans 
SET stripe_price_id_monthly = 'price_xxx', stripe_price_id_yearly = 'price_yyy'
WHERE name = 'Premium';
```

## 🎯 Usage Limits

### Free Plan
- 50 prompts per month
- 10 projects maximum
- 100 API calls per day
- Basic templates only

### Premium Plan ($7/month)
- Unlimited prompts
- Unlimited projects
- Unlimited API calls
- Access to premium templates
- Priority support

### Master Plan ($19/month)
- All Premium features
- Create master templates
- Share templates with users
- Advanced analytics
- Team management

## 🔧 Master Account Features

### Creating Master Templates

1. **Admin Access Required**: Only users with `subscription_status = 'admin'` can create master templates
2. **Template Categories**: Organize templates by themes (e.g., "Google Ads", "Real Estate")
3. **Access Control**: Set required subscription level for each template
4. **Usage Tracking**: Monitor template usage and popularity

### Template Management

```typescript
// Create a master template
const { template, error } = await MasterTemplateService.createMasterTemplate({
  title: "Google Ads Headlines",
  description: "High-converting ad headlines for Google Ads",
  category: "Marketing",
  userIdea: "Create compelling ad headlines",
  selectedStyle: "Marketing Copy",
  generatedPrompt: "Generated prompt content...",
  requiredPlan: "premium"
});
```

## 📊 Admin Dashboard

Access the admin dashboard at `/admin` with admin credentials:

### Features:
- **User Management**: View and manage user accounts
- **Subscription Overview**: Monitor active subscriptions and revenue
- **Usage Analytics**: Track system usage and popular features
- **Template Management**: Create and manage master templates
- **System Settings**: Configure API keys and system parameters

## 🔄 Usage Tracking

The system automatically tracks:
- **Prompts Generated**: Count of AI prompts created
- **Projects Created**: Number of saved projects
- **API Calls**: External API usage
- **Template Usage**: Master template access

### Integration Example:

```typescript
// Check if user can perform action
const canGenerate = await canPerformAction('prompt');
if (!canGenerate) {
  // Show upgrade prompt
  return;
}

// Perform action and increment usage
await generatePrompt(userIdea, selectedStyle);
await incrementUsage('prompt');
```

## 💳 Payment Flow

1. **User selects plan** on pricing page
2. **Stripe Checkout** handles payment processing
3. **Webhook receives** payment confirmation
4. **Database updated** with subscription details
5. **User gains access** to premium features

## 🛠️ Customization

### Adding New Subscription Plans

1. **Create in Stripe** dashboard
2. **Add to database**:
   ```sql
   INSERT INTO subscription_plans (name, description, price_monthly, features, limits)
   VALUES ('Enterprise', 'Custom enterprise solution', 49.00, '{}', '{}');
   ```
3. **Update frontend** pricing page

### Custom Usage Limits

Modify the `limits` JSONB field in subscription plans:

```json
{
  "monthly_prompts": 1000,
  "max_projects": 50,
  "api_calls_per_day": 500,
  "custom_feature": true
}
```

## 🔐 Security Considerations

- **JWT Verification**: Implement proper JWT token verification in API routes
- **Webhook Signatures**: Always verify Stripe webhook signatures
- **Rate Limiting**: Implement rate limiting on API endpoints
- **Input Validation**: Validate all user inputs
- **HTTPS Only**: Use HTTPS in production

## 📈 Monitoring & Analytics

### Key Metrics to Track:
- Monthly Recurring Revenue (MRR)
- Churn Rate
- User Acquisition Cost (CAC)
- Lifetime Value (LTV)
- Feature Usage
- API Performance

### Recommended Tools:
- **Stripe Dashboard**: Payment analytics
- **Supabase Analytics**: Database metrics
- **Vercel Analytics**: API performance
- **Custom Dashboard**: Business metrics

## 🚨 Troubleshooting

### Common Issues:

1. **Webhook Not Working**:
   - Check webhook URL is correct
   - Verify webhook secret matches
   - Check server logs for errors

2. **Payment Not Processing**:
   - Verify Stripe keys are correct
   - Check if in test mode vs live mode
   - Review Stripe dashboard for failed payments

3. **Usage Limits Not Working**:
   - Check database permissions
   - Verify usage tracking is incrementing
   - Review subscription status

## 📞 Support

For implementation support:
- Check the admin dashboard for system status
- Review Stripe dashboard for payment issues
- Monitor Supabase logs for database errors
- Contact <NAME_EMAIL>

---

## 🎉 You're All Set!

Your SaaS implementation is now complete with:
- ✅ Payment processing
- ✅ Subscription management  
- ✅ Usage tracking
- ✅ Admin dashboard
- ✅ Master account features
- ✅ Premium template system

Start monetizing your AI prompt generation platform! 🚀
