import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Trash2, Plus, Search, FileText, Eye, Edit, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';
import { MasterTemplateService, CreateMasterTemplateData, MasterTemplateFilters } from '@/lib/masterTemplates';
import { MasterTemplate } from '@/lib/supabase';

interface MasterTemplateManagerProps {
  className?: string;
}

const MasterTemplateManager: React.FC<MasterTemplateManagerProps> = ({ className }) => {
  const [templates, setTemplates] = useState<MasterTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<MasterTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPlan, setSelectedPlan] = useState<string>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [creating, setCreating] = useState(false);
  const [createForm, setCreateForm] = useState<CreateMasterTemplateData>({
    title: '',
    description: '',
    category: '',
    userIdea: '',
    selectedStyle: '',
    generatedPrompt: '',
    requiredPlan: 'free',
  });

  const categories = [
    'Marketing',
    'Google Ads',
    'Real Estate',
    'E-commerce',
    'Social Media',
    'Email Marketing',
    'Content Creation',
    'SEO',
    'Business',
    'Other'
  ];

  const styles = [
    'Professional',
    'Casual',
    'Marketing Copy',
    'Technical',
    'Creative',
    'Persuasive',
    'Educational',
    'Conversational'
  ];

  useEffect(() => {
    loadTemplates();
  }, []);

  // Client-side filtering
  useEffect(() => {
    let filtered = templates;

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(template => 
        template.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.category?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategory && selectedCategory !== 'all') {
      filtered = filtered.filter(template => template.category === selectedCategory);
    }

    // Apply plan filter
    if (selectedPlan && selectedPlan !== 'all') {
      filtered = filtered.filter(template => template.required_plan === selectedPlan);
    }

    setFilteredTemplates(filtered);
  }, [templates, searchTerm, selectedCategory, selectedPlan]);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      const { templates: templatesData, error } = await MasterTemplateService.getMasterTemplates({
        limit: 100,
      });

      if (error) {
        toast.error('Failed to load templates');
        console.error('Error loading templates:', error);
      } else {
        setTemplates(templatesData);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      toast.error('Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = async () => {
    if (!createForm.title.trim() || !createForm.category.trim() || !createForm.generatedPrompt.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    setCreating(true);
    try {
      const { template, error } = await MasterTemplateService.createMasterTemplate(createForm);

      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Template created successfully');
        setShowCreateDialog(false);
        setCreateForm({
          title: '',
          description: '',
          category: '',
          userIdea: '',
          selectedStyle: '',
          generatedPrompt: '',
          requiredPlan: 'free',
        });
        loadTemplates();
      }
    } catch (error) {
      console.error('Error creating template:', error);
      toast.error('Failed to create template');
    } finally {
      setCreating(false);
    }
  };

  const handleDeleteTemplate = async (templateId: string, templateTitle: string) => {
    if (!confirm(`Are you sure you want to delete "${templateTitle}"?`)) {
      return;
    }

    try {
      const { success, error } = await MasterTemplateService.deleteMasterTemplate(templateId);

      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Template deleted successfully');
        loadTemplates();
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      toast.error('Failed to delete template');
    }
  };

  const handleToggleActive = async (templateId: string, currentStatus: boolean) => {
    try {
      const { template, error } = await MasterTemplateService.updateMasterTemplate(templateId, {
        is_active: !currentStatus
      });

      if (error) {
        toast.error(error.message);
      } else {
        toast.success(`Template ${!currentStatus ? 'activated' : 'deactivated'} successfully`);
        loadTemplates();
      }
    } catch (error) {
      console.error('Error updating template:', error);
      toast.error('Failed to update template');
    }
  };

  const getPlanBadge = (plan: string) => {
    const variants = {
      free: 'secondary',
      premium: 'default',
      admin: 'destructive',
    } as const;

    return (
      <Badge variant={variants[plan as keyof typeof variants] || 'secondary'}>
        {plan}
      </Badge>
    );
  };

  const uniqueCategories = Array.from(new Set(templates.map(t => t.category).filter(Boolean)));

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Master Templates
          </CardTitle>
          <CardDescription>
            Manage premium template collections for your users
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Controls */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search templates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {uniqueCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedPlan} onValueChange={setSelectedPlan}>
              <SelectTrigger className="w-full sm:w-[150px]">
                <SelectValue placeholder="Filter by plan" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All plans</SelectItem>
                <SelectItem value="free">Free</SelectItem>
                <SelectItem value="premium">Premium</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Template
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Create Master Template</DialogTitle>
                  <DialogDescription>
                    Create a new template that users can access based on their subscription plan
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="title">Title *</Label>
                      <Input
                        id="title"
                        value={createForm.title}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, title: e.target.value }))}
                        placeholder="e.g., Google Ads Headlines"
                      />
                    </div>
                    <div>
                      <Label htmlFor="category">Category *</Label>
                      <Select value={createForm.category} onValueChange={(value) => setCreateForm(prev => ({ ...prev, category: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={createForm.description}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of what this template does..."
                      rows={2}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="userIdea">User Idea</Label>
                      <Input
                        id="userIdea"
                        value={createForm.userIdea}
                        onChange={(e) => setCreateForm(prev => ({ ...prev, userIdea: e.target.value }))}
                        placeholder="e.g., Create compelling ad headlines"
                      />
                    </div>
                    <div>
                      <Label htmlFor="selectedStyle">Style</Label>
                      <Select value={createForm.selectedStyle} onValueChange={(value) => setCreateForm(prev => ({ ...prev, selectedStyle: value }))}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select style" />
                        </SelectTrigger>
                        <SelectContent>
                          {styles.map((style) => (
                            <SelectItem key={style} value={style}>
                              {style}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="generatedPrompt">Generated Prompt *</Label>
                    <Textarea
                      id="generatedPrompt"
                      value={createForm.generatedPrompt}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, generatedPrompt: e.target.value }))}
                      placeholder="The actual prompt content that users will receive..."
                      rows={4}
                    />
                  </div>
                  <div>
                    <Label htmlFor="requiredPlan">Required Plan</Label>
                    <Select value={createForm.requiredPlan} onValueChange={(value) => setCreateForm(prev => ({ ...prev, requiredPlan: value as 'free' | 'premium' | 'admin' }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="free">Free - Available to all users</SelectItem>
                        <SelectItem value="premium">Premium - Requires premium subscription</SelectItem>
                        <SelectItem value="admin">Admin - Admin only</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateTemplate} disabled={creating}>
                    {creating ? 'Creating...' : 'Create Template'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Templates List */}
          <div className="space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground mt-2">Loading templates...</p>
              </div>
            ) : filteredTemplates.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No templates found</p>
                <p className="text-sm text-muted-foreground">
                  {searchTerm || selectedCategory !== 'all' || selectedPlan !== 'all' 
                    ? 'Try adjusting your filters' 
                    : 'Create your first master template to get started'
                  }
                </p>
              </div>
            ) : (
              filteredTemplates.map((template) => (
                <Card key={template.id} className={`${!template.is_active ? 'opacity-60' : ''}`}>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-medium">{template.title}</h4>
                          <Badge variant="outline">{template.category}</Badge>
                          {getPlanBadge(template.required_plan)}
                          {!template.is_active && (
                            <Badge variant="secondary">Inactive</Badge>
                          )}
                        </div>
                        {template.description && (
                          <p className="text-sm text-muted-foreground mb-2">{template.description}</p>
                        )}
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <TrendingUp className="h-3 w-3" />
                            <span>{template.usage_count} uses</span>
                          </div>
                          <span>•</span>
                          <span>Created {new Date(template.created_at).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleActive(template.id, template.is_active)}
                        >
                          {template.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteTemplate(template.id, template.title)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Summary */}
          {!loading && filteredTemplates.length > 0 && (
            <div className="pt-4 border-t">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>
                  Showing {filteredTemplates.length} of {templates.length} templates
                </span>
                <Button variant="outline" size="sm" onClick={loadTemplates}>
                  Refresh
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MasterTemplateManager;
