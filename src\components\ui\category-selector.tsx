import React, { useState, useEffect } from 'react';
import { Check, ChevronDown, Plus, Folder, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from './button';
import { Input } from './input';
import { Label } from './label';
import { Popover, PopoverContent, PopoverTrigger } from './popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from './command';
import { Badge } from './badge';
import { TagsService, Category } from '@/lib/tagsService';

export interface CategorySelectorProps {
  selectedCategories: string[];
  onCategoriesChange: (categories: string[]) => void;
  placeholder?: string;
  maxCategories?: number;
  allowMultiple?: boolean;
  allowCustom?: boolean;
  className?: string;
  disabled?: boolean;
}

export function CategorySelector({
  selectedCategories,
  onCategoriesChange,
  placeholder = "Select categories...",
  maxCategories = 5,
  allowMultiple = true,
  allowCustom = true,
  className,
  disabled = false
}: CategorySelectorProps) {
  const [open, setOpen] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryColor, setNewCategoryColor] = useState('#10B981');

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    setLoading(true);
    try {
      const { categories: fetchedCategories, error } = await TagsService.getCategories({
        sortBy: 'usage_count',
        sortOrder: 'desc'
      });

      if (error) {
        console.error('Error loading categories:', error);
      } else {
        setCategories(fetchedCategories);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySelect = (categoryName: string) => {
    if (disabled) return;

    if (allowMultiple) {
      if (selectedCategories.includes(categoryName)) {
        // Remove category
        onCategoriesChange(selectedCategories.filter(cat => cat !== categoryName));
      } else if (selectedCategories.length < maxCategories) {
        // Add category
        onCategoriesChange([...selectedCategories, categoryName]);
      }
    } else {
      // Single selection
      onCategoriesChange([categoryName]);
      setOpen(false);
    }
  };

  const handleCreateCategory = async () => {
    if (!newCategoryName.trim() || !allowCustom) return;

    try {
      const { category, error } = await TagsService.createCategory(
        newCategoryName.trim(),
        newCategoryColor,
        `Custom category: ${newCategoryName.trim()}`
      );

      if (error) {
        console.error('Error creating category:', error);
        return;
      }

      if (category) {
        setCategories(prev => [category, ...prev]);
        handleCategorySelect(category.name);
        setNewCategoryName('');
        setNewCategoryColor('#10B981');
        setShowCreateForm(false);
      }
    } catch (error) {
      console.error('Error creating category:', error);
    }
  };

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchValue.toLowerCase())
  );

  const selectedCategoryObjects = categories.filter(cat => 
    selectedCategories.includes(cat.name)
  );

  const getCategoryColor = (category: Category): string => {
    return category.color || '#10B981';
  };

  const displayText = selectedCategories.length === 0 
    ? placeholder 
    : allowMultiple 
      ? `${selectedCategories.length} selected`
      : selectedCategories[0];

  return (
    <div className={cn("space-y-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between",
              disabled && "cursor-not-allowed opacity-50"
            )}
            disabled={disabled}
          >
            <div className="flex items-center gap-2">
              <Folder className="w-4 h-4 text-muted-foreground" />
              <span className="truncate">{displayText}</span>
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput 
              placeholder="Search categories..." 
              value={searchValue}
              onValueChange={setSearchValue}
            />
            <CommandList>
              {loading ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  Loading categories...
                </div>
              ) : (
                <>
                  <CommandEmpty>
                    <div className="p-4 text-center">
                      <p className="text-sm text-muted-foreground mb-2">
                        No categories found.
                      </p>
                      {allowCustom && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowCreateForm(true)}
                          className="text-xs"
                        >
                          <Plus className="w-3 h-3 mr-1" />
                          Create "{searchValue}"
                        </Button>
                      )}
                    </div>
                  </CommandEmpty>
                  
                  <CommandGroup>
                    {filteredCategories.map((category) => (
                      <CommandItem
                        key={category.id}
                        value={category.name}
                        onSelect={() => handleCategorySelect(category.name)}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: getCategoryColor(category) }}
                          />
                          <span className="flex items-center gap-1">
                            {category.icon && <span>{category.icon}</span>}
                            {category.name}
                          </span>
                          {category.usage_count > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {category.usage_count}
                            </Badge>
                          )}
                        </div>
                        {selectedCategories.includes(category.name) && (
                          <Check className="h-4 w-4" />
                        )}
                      </CommandItem>
                    ))}
                  </CommandGroup>

                  {allowCustom && !showCreateForm && (
                    <div className="p-2 border-t">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowCreateForm(true)}
                        className="w-full justify-start text-xs"
                      >
                        <Plus className="w-3 h-3 mr-2" />
                        Create new category
                      </Button>
                    </div>
                  )}

                  {showCreateForm && (
                    <div className="p-3 border-t space-y-3">
                      <div className="space-y-2">
                        <Label htmlFor="category-name" className="text-xs">
                          Category Name
                        </Label>
                        <Input
                          id="category-name"
                          value={newCategoryName}
                          onChange={(e) => setNewCategoryName(e.target.value)}
                          placeholder="Enter category name..."
                          className="h-8 text-xs"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="category-color" className="text-xs">
                          Color
                        </Label>
                        <div className="flex items-center gap-2">
                          <input
                            id="category-color"
                            type="color"
                            value={newCategoryColor}
                            onChange={(e) => setNewCategoryColor(e.target.value)}
                            className="w-8 h-8 rounded border"
                          />
                          <Input
                            value={newCategoryColor}
                            onChange={(e) => setNewCategoryColor(e.target.value)}
                            placeholder="#10B981"
                            className="h-8 text-xs"
                          />
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          onClick={handleCreateCategory}
                          disabled={!newCategoryName.trim()}
                          className="flex-1 h-8 text-xs"
                        >
                          Create
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setShowCreateForm(false);
                            setNewCategoryName('');
                            setNewCategoryColor('#10B981');
                          }}
                          className="flex-1 h-8 text-xs"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  )}
                </>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected categories display */}
      {selectedCategories.length > 0 && allowMultiple && (
        <div className="flex flex-wrap gap-1">
          {selectedCategoryObjects.map((category) => (
            <Badge
              key={category.id}
              variant="secondary"
              className="text-xs font-medium border"
              style={{ 
                backgroundColor: `${getCategoryColor(category)}20`,
                borderColor: getCategoryColor(category),
                color: getCategoryColor(category)
              }}
            >
              <div className="flex items-center gap-1">
                {category.icon && <span>{category.icon}</span>}
                {category.name}
                {!disabled && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-auto p-0 text-current hover:bg-transparent opacity-70 hover:opacity-100"
                    onClick={() => handleCategorySelect(category.name)}
                  >
                    ×
                  </Button>
                )}
              </div>
            </Badge>
          ))}
        </div>
      )}

      {/* Category limit indicator */}
      {allowMultiple && maxCategories && (
        <div className="text-xs text-muted-foreground">
          {selectedCategories.length}/{maxCategories} categories
        </div>
      )}
    </div>
  );
}
