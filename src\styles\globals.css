/* Scrollbar Styles */
.prose::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.prose::-webkit-scrollbar-track {
  background: transparent;
}

.prose::-webkit-scrollbar-thumb {
  background-color: #93C5FD;
  border-radius: 20px;
  border: 3px solid transparent;
}

/* Markdown Styles */
.prose {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4 {
  color: #1e40af;
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose p {
  margin-bottom: 1em;
}

.prose ul,
.prose ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}

.prose code {
  background-color: #f1f5f9;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-size: 0.9em;
  color: #0f172a;
}

.prose pre {
  background-color: #f8fafc;
  padding: 1em;
  border-radius: 0.5em;
  overflow-x: auto;
  margin: 1em 0;
}

.prose blockquote {
  border-left: 4px solid #93c5fd;
  padding-left: 1em;
  margin: 1em 0;
  color: #475569;
}