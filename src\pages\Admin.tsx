import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import BackgroundEffects from '@/components/BackgroundEffects';
import { getGroqConfig, saveGroqConfig } from '@/lib/groq';
import { UsageService } from '@/lib/usage';
import { StripeService } from '@/lib/stripe';
import { MasterTemplateService } from '@/lib/masterTemplates';
import { supabase } from '@/lib/supabase';
import ProjectAccessManager from '@/components/admin/ProjectAccessManager';
import MasterTemplateManager from '@/components/admin/MasterTemplateManager';
import {
  LogOut,
  Users,
  CreditCard,
  BarChart3,
  Settings,
  FileText,
  DollarSign,
  TrendingUp,
  Activity,
  Shield
} from 'lucide-react';

const Admin = () => {
  const { signOut, profile, isAdmin, loading } = useAuth();
  const [apiKey, setApiKey] = useState('');
  const [modelName, setModelName] = useState('mixtral-8x7b-32768');
  const [stats, setStats] = useState<any>(null);
  const [users, setUsers] = useState<any[]>([]);
  const [subscriptions, setSubscriptions] = useState<any[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [dashboardLoading, setDashboardLoading] = useState(true);

  // Debug admin access
  console.log('Admin Component Debug:', {
    profile,
    isAdmin,
    loading,
    subscription_status: profile?.subscription_status
  });

  // Check admin access
  if (loading) {
    return (
      <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
        <BackgroundEffects />
        <div className="relative z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
        <BackgroundEffects />
        <div className="relative z-10 text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
          <p className="text-sm text-gray-500 mb-4">
            Current status: {profile?.subscription_status || 'unknown'}
          </p>
          <Button onClick={() => window.location.href = '/'}>
            Go Home
          </Button>
        </div>
      </div>
    );
  }

  useEffect(() => {
    const config = getGroqConfig();
    setApiKey(config.apiKey);
    setModelName(config.model);
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setDashboardLoading(true);
    try {
      // Load usage stats
      const { stats: usageStats } = await UsageService.getUsageStats();
      setStats(usageStats);

      // Load users
      const { data: usersData } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);
      setUsers(usersData || []);

      // Load subscriptions
      const { data: subsData } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          user:profiles(full_name, email),
          plan:subscription_plans(name, price_monthly)
        `)
        .order('created_at', { ascending: false })
        .limit(50);
      setSubscriptions(subsData || []);

      // Load master templates
      const { templates: templatesData } = await MasterTemplateService.getMasterTemplates({
        limit: 50,
        sortBy: 'usage_count',
        sortOrder: 'desc'
      });
      setTemplates(templatesData);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setDashboardLoading(false);
    }
  };

  const handleSave = () => {
    if (!apiKey.trim()) {
      toast.error('API Key is required');
      return;
    }

    const saved = saveGroqConfig(apiKey, modelName);

    if (saved) {
      toast.success('GROQ API configuration saved');
    } else {
      toast.info('In production, configure GROQ using environment variables');
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundEffects />

      <div className="relative z-10">
        <header className="container max-w-7xl mx-auto px-6 py-4 flex justify-between items-center">
          <h1 className="text-3xl font-bold">
            <span className="text-highlight">Admin</span> Dashboard
          </h1>
          <Button
            variant="outline"
            onClick={signOut}
            className="flex items-center gap-2"
          >
            <LogOut className="w-4 h-4" />
            Logout
          </Button>
        </header>

        <main className="container max-w-7xl mx-auto px-6 py-8">
          {dashboardLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <Tabs defaultValue="overview" className="space-y-6">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="users" className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Users
                </TabsTrigger>
                <TabsTrigger value="subscriptions" className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  Subscriptions
                </TabsTrigger>
                <TabsTrigger value="templates" className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Templates
                </TabsTrigger>
                <TabsTrigger value="access" className="flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  Access
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Settings
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{stats?.total_users || 0}</div>
                      <p className="text-xs text-muted-foreground">
                        {stats?.active_users || 0} active this month
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Prompts</CardTitle>
                      <Activity className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{stats?.total_prompts || 0}</div>
                      <p className="text-xs text-muted-foreground">Generated this month</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Projects Created</CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{stats?.total_projects || 0}</div>
                      <p className="text-xs text-muted-foreground">This month</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">API Calls</CardTitle>
                      <BarChart3 className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{stats?.total_api_calls || 0}</div>
                      <p className="text-xs text-muted-foreground">This month</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        ${(stats?.monthly_revenue || 0).toFixed(2)}
                      </div>
                      <p className="text-xs text-muted-foreground">This month</p>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Users</CardTitle>
                      <CardDescription>Latest user registrations</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {users.slice(0, 5).map((user) => (
                          <div key={user.id} className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">{user.full_name || 'Anonymous'}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                            <Badge variant={user.subscription_status === 'premium' ? 'default' : 'secondary'}>
                              {user.subscription_status}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Popular Templates</CardTitle>
                      <CardDescription>Most used master templates</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {templates.slice(0, 5).map((template) => (
                          <div key={template.id} className="flex items-center justify-between">
                            <div>
                              <p className="font-medium">{template.title}</p>
                              <p className="text-sm text-muted-foreground">{template.category}</p>
                            </div>
                            <Badge variant="outline">
                              {template.usage_count} uses
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="users" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>User Management</CardTitle>
                    <CardDescription>Manage user accounts and subscriptions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {users.map((user) => (
                        <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center gap-4">
                              <div>
                                <p className="font-medium">{user.full_name || 'Anonymous'}</p>
                                <p className="text-sm text-muted-foreground">{user.email}</p>
                                <p className="text-xs text-muted-foreground">
                                  Joined: {new Date(user.created_at).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={user.subscription_status === 'premium' ? 'default' : 'secondary'}>
                              {user.subscription_status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="subscriptions" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Subscription Management</CardTitle>
                    <CardDescription>Monitor active subscriptions and revenue</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {subscriptions.map((sub) => (
                        <div key={sub.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center gap-4">
                              <div>
                                <p className="font-medium">{sub.user?.full_name || 'Anonymous'}</p>
                                <p className="text-sm text-muted-foreground">{sub.user?.email}</p>
                                <p className="text-xs text-muted-foreground">
                                  Plan: {sub.plan?.name} - ${sub.plan?.price_monthly}/month
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={sub.status === 'active' ? 'default' : 'destructive'}>
                              {sub.status}
                            </Badge>
                            {sub.current_period_end && (
                              <p className="text-xs text-muted-foreground">
                                Expires: {new Date(sub.current_period_end).toLocaleDateString()}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="templates" className="space-y-6">
                <MasterTemplateManager />
              </TabsContent>

              <TabsContent value="access" className="space-y-6">
                <ProjectAccessManager />
              </TabsContent>

              <TabsContent value="settings" className="space-y-6">
                <Card className="glass-panel">
                  <CardHeader>
                    <CardTitle>GROQ API Configuration</CardTitle>
                    <CardDescription>
                      {import.meta.env.PROD
                        ? 'In production, configure GROQ using environment variables'
                        : 'Configure your GROQ API settings here'}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <label className="text-sm font-medium leading-none">
                        API Key
                      </label>
                      <Input
                        type="password"
                        placeholder="Enter your GROQ API key"
                        value={apiKey}
                        onChange={(e) => setApiKey(e.target.value)}
                        className="input-focus-ring bg-white/50"
                        disabled={import.meta.env.PROD}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium leading-none">
                        Model Name
                      </label>
                      <Input
                        type="text"
                        placeholder="Enter model name"
                        value={modelName}
                        onChange={(e) => setModelName(e.target.value)}
                        className="input-focus-ring bg-white/50"
                        disabled={import.meta.env.PROD}
                      />
                      <p className="text-sm text-muted-foreground">
                        Default: mixtral-8x7b-32768
                      </p>
                    </div>

                    <Button
                      onClick={handleSave}
                      className="btn-primary w-full"
                      disabled={import.meta.env.PROD}
                    >
                      Save Configuration
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>System Settings</CardTitle>
                    <CardDescription>Configure system-wide settings</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Button
                      variant="outline"
                      onClick={loadDashboardData}
                      className="w-full"
                    >
                      Refresh Dashboard Data
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => toast.info('Feature coming soon!')}
                      className="w-full"
                    >
                      Export User Data
                    </Button>

                    <Button
                      variant="outline"
                      onClick={() => toast.info('Feature coming soon!')}
                      className="w-full"
                    >
                      System Maintenance
                    </Button>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          )}
        </main>
      </div>
    </div>
  );
};

export default Admin;


