# ChatsPrompt

A professional AI prompt engineering tool built with modern web technologies.

## Project info

ChatsPrompt helps you craft perfect prompts for any AI model in seconds.

## Technologies Used

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## Getting Started

1. Clone this repository
2. Install dependencies:
```bash
npm install
```
3. Create a `.env` file based on `.env.example` and add your GROQ API key
4. Start the development server:
```bash
npm run dev
```

## Building for Production

```bash
npm run build
```

## Deployment

You can deploy this application to any static hosting service like Netlify, Vercel, or GitHub Pages.

## Environment Variables

The following environment variables are required:

- `VITE_GROQ_API_KEY`: Your GROQ API key
- `VITE_GROQ_MODEL`: The GROQ model to use (default: llama-3.3-70b-versatile)
- `VITE_ADMIN_USERNAME`: Admin username for the dashboard
- `VITE_ADMIN_PASSWORD`: Admin password for the dashboard
