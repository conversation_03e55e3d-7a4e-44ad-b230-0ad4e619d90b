
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Check } from "lucide-react";
import { useState } from "react";
import { PromptStyle, promptStyles } from '@/utils/promptTemplates';

interface Props {
  value: string;
  onChange: (value: string) => void;
}

const PromptStyleSelector = ({ value, onChange }: Props) => {
  const [open, setOpen] = useState(false);
  const selectedStyle = promptStyles.find(style => style.id === value);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full h-20 px-8 rounded-full shadow-lg bg-white/80 backdrop-blur-sm border-2 border-blue-500/20 focus:outline-none focus:border-blue-500/40 focus:ring-2 focus:ring-blue-500/30 transition-all text-lg"
        >
          <div className="flex flex-col items-start w-full gap-1">
            {selectedStyle ? (
              <>
                <span className="font-medium text-base">{selectedStyle.name}</span>
                <span className="text-sm text-gray-500">{selectedStyle.description}</span>
              </>
            ) : (
              <span className="text-gray-500">Choose your prompt style...</span>
            )}
          </div>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px] p-0 gap-0">
        <div className="p-4 space-y-2">
          {promptStyles.map((style) => (
            <button
              key={style.id}
              className={`relative flex w-full cursor-pointer select-none items-start py-4 px-8 hover:bg-blue-50 rounded-[20px] transition-colors ${
                value === style.id ? 'bg-blue-50' : ''
              }`}
              onClick={() => {
                onChange(style.id);
                setOpen(false);
              }}
            >
              <div className="flex flex-col items-start text-left w-full gap-1">
                <span className="font-medium text-base">{style.name}</span>
                <span className="text-sm text-gray-500 text-left">{style.description}</span>
              </div>
              {value === style.id && (
                <span className="absolute left-2 top-1/2 -translate-y-1/2 flex h-3.5 w-3.5 items-center justify-center">
                  <Check className="h-4 w-4 text-blue-500" />
                </span>
              )}
            </button>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PromptStyleSelector;
