import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import BackgroundEffects from '@/components/BackgroundEffects';
import Logo from '@/components/Logo';
import { StripeService, SubscriptionWithPlan } from '@/lib/stripe';
import { UsageService, CurrentUsage, UsageLimits } from '@/lib/usage';
import { PaymentHistory } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import {
  CreditCard,
  BarChart3,
  <PERSON><PERSON>s,
  ArrowLeft,
  Loader2,
  Crown,
  Calendar,
  TrendingUp,
  Zap,
  Receipt,
  CheckCircle,
  XCircle,
  Clock,
  ExternalLink,
  Sparkles
} from 'lucide-react';
import { motion } from 'framer-motion';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, profile } = useAuth();
  const [subscription, setSubscription] = useState<SubscriptionWithPlan | null>(null);
  const [usage, setUsage] = useState<CurrentUsage | null>(null);
  const [limits, setLimits] = useState<UsageLimits | null>(null);
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const [canceling, setCanceling] = useState(false);

  // Check if user has premium access (fallback to profile status)
  const hasPremiumAccess = () => {
    return subscription?.status === 'active' || 
           profile?.subscription_status === 'premium' || 
           profile?.subscription_status === 'admin' ||
           user?.email === '<EMAIL>';
  };

  const getCurrentPlanName = () => {
    if (subscription?.plan?.name) return subscription.plan.name;
    if (profile?.subscription_status === 'premium') return 'Premium';
    if (profile?.subscription_status === 'admin') return 'Admin';
    return 'Free';
  };

  useEffect(() => {
    loadDashboardData();

    // Check for success param
    if (searchParams.get('success') === 'true') {
      toast.success('Welcome to your dashboard!');
    }
  }, [searchParams]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      console.log('Loading dashboard data for user:', user?.email);
      console.log('Profile subscription status:', profile?.subscription_status);
      
      const [subscriptionResult, usageResult, limitsResult, paymentsResult] = await Promise.all([
        StripeService.getUserSubscription(),
        UsageService.getCurrentUsage(),
        UsageService.getUserLimits(),
        StripeService.getPaymentHistory(),
      ]);

      console.log('Subscription result:', subscriptionResult);
      console.log('Payments result:', paymentsResult);

      if (subscriptionResult.subscription) {
        setSubscription(subscriptionResult.subscription);
      }

      if (usageResult.usage) {
        setUsage(usageResult.usage);
      }

      if (limitsResult.limits) {
        setLimits(limitsResult.limits);
      }

      if (paymentsResult.payments) {
        setPaymentHistory(paymentsResult.payments);
        console.log('Payment history loaded:', paymentsResult.payments.length, 'payments');
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleManageBilling = async () => {
    try {
      const { url, error } = await StripeService.createPortalSession();
      if (error) {
        toast.error(error.message);
        return;
      }
      if (url) {
        window.location.href = url;
      }
    } catch (error) {
      console.error('Error creating portal session:', error);
      toast.error('Failed to open billing portal');
    }
  };

  const handleCancelSubscription = async () => {
    if (!confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.')) {
      return;
    }

    setCanceling(true);
    try {
      const { success, error } = await StripeService.cancelSubscription();
      if (error) {
        toast.error(error.message);
        return;
      }
      if (success) {
        toast.success('Subscription canceled successfully');
        loadDashboardData(); // Refresh data
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error('Failed to cancel subscription');
    } finally {
      setCanceling(false);
    }
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatDateLong = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'succeeded':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
        <BackgroundEffects />
        <div className="relative z-10">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundEffects />
      
      <div className="relative z-10">
        <header className="flex justify-between items-center p-6">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => navigate('/')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
            <Logo />
          </div>
        </header>

        <main className="container max-w-6xl mx-auto px-6 py-8">
     

          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="subscription" className="flex items-center gap-2">
                <CreditCard className="w-4 h-4" />
                Subscription
              </TabsTrigger>
              <TabsTrigger value="usage" className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                Usage
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <div className="mb-6">
                <h2 className="text-2xl font-semibold mb-2">
                  Welcome back, <span className="text-highlight">{profile?.full_name || 'User'}</span>
                </h2>
                <p className="text-gray-600">
                  {subscription
                    ? `Manage your ${subscription.plan.name} subscription and track your usage`
                    : 'Manage your account and explore premium features'
                  }
                </p>
                {subscription && (
                  <div className="mt-4 flex items-center gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Crown className="w-4 h-4 text-yellow-500" />
                      <span>{subscription.plan.name} Plan</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>
                        {subscription.cancel_at_period_end ? 'Expires' : 'Renews'} on {formatDateLong(subscription.current_period_end)}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className={hasPremiumAccess() ? 'border-green-200 bg-green-50/30' : 'border-blue-200 bg-blue-50/30'}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Current Plan</CardTitle>
                    <Crown className={`h-4 w-4 ${hasPremiumAccess() ? 'text-green-500' : 'text-blue-500'}`} />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {getCurrentPlanName()}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {subscription?.plan ? formatCurrency(subscription.plan.price_monthly) + '/month' : hasPremiumAccess() ? 'Premium access' : 'No subscription'}
                    </p>
                    {(subscription?.status || hasPremiumAccess()) && (
                      <Badge variant={subscription?.status === 'active' || hasPremiumAccess() ? 'default' : 'destructive'} className="mt-2 text-xs">
                        {subscription?.status || 'active'}
                      </Badge>
                    )}
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Prompts Used</CardTitle>
                    <Zap className="h-4 w-4 text-yellow-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{usage?.prompts_generated || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      {limits?.monthly_prompts === -1 ? 'Unlimited' : `of ${limits?.monthly_prompts || 0} this month`}
                    </p>
                    {limits?.monthly_prompts !== -1 && limits?.monthly_prompts && (
                      <div className="mt-2">
                        <Progress
                          value={getUsagePercentage(usage?.prompts_generated || 0, limits.monthly_prompts)}
                          className="h-1"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Projects</CardTitle>
                    <BarChart3 className="h-4 w-4 text-blue-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{usage?.projects_created || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      {limits?.max_projects === -1 ? 'Unlimited' : `of ${limits?.max_projects || 0} max`}
                    </p>
                    {limits?.max_projects !== -1 && limits?.max_projects && (
                      <div className="mt-2">
                        <Progress
                          value={getUsagePercentage(usage?.projects_created || 0, limits.max_projects)}
                          className="h-1"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card className="hover:shadow-md transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">API Calls</CardTitle>
                    <TrendingUp className="h-4 w-4 text-purple-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{usage?.api_calls || 0}</div>
                    <p className="text-xs text-muted-foreground">This month</p>
                    <div className="mt-2">
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span>Active</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions and Upgrade Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Quick Actions */}
                <Card className="bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="w-5 h-5" />
                      Quick Actions
                    </CardTitle>
                    <CardDescription>Manage your account and subscription</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-3">
                      <Button onClick={() => navigate('/')} variant="outline" className="flex items-center gap-2">
                        <ArrowLeft className="w-4 h-4" />
                        Back to App
                      </Button>
                      <Button onClick={handleManageBilling} variant="outline" className="flex items-center gap-2">
                        <CreditCard className="w-4 h-4" />
                        Manage Billing
                      </Button>
                      <Button onClick={() => navigate('/pricing')} variant="outline" className="flex items-center gap-2">
                        <Crown className="w-4 h-4" />
                        View Plans
                      </Button>
                      {subscription && !subscription.cancel_at_period_end && (
                        <Button
                          onClick={handleCancelSubscription}
                          variant="destructive"
                          disabled={canceling}
                          className="flex items-center gap-2"
                        >
                          {canceling ? (
                            <>
                              <Loader2 className="w-4 h-4 animate-spin" />
                              Canceling...
                            </>
                          ) : (
                            'Cancel Subscription'
                          )}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Upgrade/Status Section */}
                {!hasPremiumAccess() ? (
                  <Card className="border-2 border-dashed border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Sparkles className="w-5 h-5 text-blue-500" />
                        Upgrade Your Plan
                      </CardTitle>
                      <CardDescription>Unlock premium features and unlimited usage</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="text-sm text-muted-foreground">
                          ✨ Unlimited prompts & projects<br/>
                          🚀 Priority support<br/>
                          🎯 Advanced features
                        </div>
                        <Button onClick={() => navigate('/pricing')} className="w-full">
                          View Pricing Plans
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card className="border-green-200 bg-green-50">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-green-800">
                        <CheckCircle className="w-5 h-5" />
                        Premium Active
                      </CardTitle>
                      <CardDescription className="text-green-700">
                        You're enjoying all premium features
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <p className="text-sm text-green-700">
                          Thank you for being a premium member! Need to upgrade or change plans?
                        </p>
                        <Button onClick={() => navigate('/pricing')} variant="outline" className="w-full">
                          View All Plans
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            <TabsContent value="subscription" className="space-y-6">
              {/* Subscription Details */}
              {subscription && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Crown className="w-5 h-5 text-yellow-500" />
                      Current Subscription
                    </CardTitle>
                    <CardDescription>Your subscription details and status</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">Plan</label>
                        <p className="text-lg font-semibold">{subscription.plan.name}</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">Price</label>
                        <p className="text-lg font-semibold">{formatCurrency(subscription.plan.price_monthly)}/month</p>
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-muted-foreground">Status</label>
                        <Badge variant={subscription.status === 'active' ? 'default' : 'destructive'}>
                          {subscription.status}
                        </Badge>
                      </div>
                    </div>

                    {subscription.current_period_end && (
                      <div className="mt-4 p-3 bg-muted rounded-lg">
                        <div className="flex items-center gap-2">
                          <Calendar className="w-4 h-4 text-muted-foreground" />
                          <span className="text-sm">
                            {subscription.cancel_at_period_end ? 'Expires' : 'Renews'} on {formatDate(subscription.current_period_end)}
                          </span>
                        </div>
                      </div>
                    )}

                    <div className="flex gap-2 mt-4">
                      <Button onClick={handleManageBilling} variant="outline" className="flex items-center gap-2">
                        <ExternalLink className="w-4 h-4" />
                        Manage Billing
                      </Button>
                      {!subscription.cancel_at_period_end && (
                        <Button
                          onClick={handleCancelSubscription}
                          variant="destructive"
                          disabled={canceling}
                        >
                          {canceling ? (
                            <>
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              Canceling...
                            </>
                          ) : (
                            'Cancel Subscription'
                          )}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Payment History / Invoices */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Receipt className="w-5 h-5" />
                    {subscription ? 'Recent Invoices' : 'Payment History'}
                  </CardTitle>
                  <CardDescription>
                    {subscription
                      ? 'Your recent billing history and invoices'
                      : 'View your payment history when you upgrade'
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {paymentHistory.length > 0 ? (
                    <div className="space-y-3">
                      {paymentHistory.slice(0, 5).map((payment) => (
                        <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center gap-3">
                            {getPaymentStatusIcon(payment.status)}
                            <div>
                              <p className="font-medium">
                                {payment.description || 'Subscription Payment'}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {formatDate(payment.created_at)}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">
                              {formatCurrency(payment.amount, payment.currency)}
                            </p>
                            <Badge
                              variant="secondary"
                              className={`text-xs ${getPaymentStatusColor(payment.status)}`}
                            >
                              {payment.status}
                            </Badge>
                          </div>
                        </div>
                      ))}

                      {paymentHistory.length > 5 && (
                        <div className="text-center pt-2">
                          <Button variant="outline" onClick={handleManageBilling}>
                            View All Invoices
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Receipt className="w-12 h-12 text-muted-foreground mx-auto mb-3" />
                      <p className="text-muted-foreground">
                        {hasPremiumAccess()
                          ? 'Payment history is being processed. Check back soon or use the billing portal.'
                          : 'Upgrade to a premium plan to see your payment history'
                        }
                      </p>
                      {hasPremiumAccess() ? (
                        <Button onClick={handleManageBilling} className="mt-3">
                          View Billing Portal
                        </Button>
                      ) : (
                        <Button onClick={() => navigate('/pricing')} className="mt-3">
                          View Plans
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="usage" className="space-y-6">
              {/* Usage tracking content */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Monthly Usage</CardTitle>
                    <CardDescription>Your usage for {usage?.month_year}</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Prompts Generated</span>
                        <span>{usage?.prompts_generated || 0} / {limits?.monthly_prompts === -1 ? '∞' : limits?.monthly_prompts}</span>
                      </div>
                      <Progress 
                        value={getUsagePercentage(usage?.prompts_generated || 0, limits?.monthly_prompts || 0)} 
                        className="h-2"
                      />
                    </div>
                    
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Projects Created</span>
                        <span>{usage?.projects_created || 0} / {limits?.max_projects === -1 ? '∞' : limits?.max_projects}</span>
                      </div>
                      <Progress 
                        value={getUsagePercentage(usage?.projects_created || 0, limits?.max_projects || 0)} 
                        className="h-2"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;










