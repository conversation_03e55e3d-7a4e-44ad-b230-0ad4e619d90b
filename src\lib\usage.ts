import { supabase, UserUsage, SubscriptionPlan } from './supabase';

export interface UsageLimits {
  monthly_prompts: number; // -1 for unlimited
  max_projects: number; // -1 for unlimited
  api_calls_per_day: number; // -1 for unlimited
}

export interface CurrentUsage {
  prompts_generated: number;
  projects_created: number;
  api_calls: number;
  month_year: string;
}

export class UsageService {
  /**
   * Get current month's usage for user
   */
  static async getCurrentUsage(): Promise<{ usage: CurrentUsage | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { usage: null, error: new Error('User not authenticated') };
      }

      const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

      const { data: usage, error } = await supabase
        .from('user_usage')
        .select('*')
        .eq('user_id', user.id)
        .eq('month_year', currentMonth)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        return { usage: null, error: new Error(error.message) };
      }

      // If no usage record exists, create one
      if (!usage) {
        const newUsage = {
          user_id: user.id,
          month_year: currentMonth,
          prompts_generated: 0,
          projects_created: 0,
          api_calls: 0,
        };

        const { data: createdUsage, error: createError } = await supabase
          .from('user_usage')
          .insert(newUsage)
          .select()
          .single();

        if (createError) {
          return { usage: null, error: new Error(createError.message) };
        }

        return { usage: createdUsage, error: null };
      }

      return { usage, error: null };
    } catch (error) {
      console.error('Error fetching current usage:', error);
      return { usage: null, error: error as Error };
    }
  }

  /**
   * Get user's subscription limits
   */
  static async getUserLimits(): Promise<{ limits: UsageLimits | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { limits: null, error: new Error('User not authenticated') };
      }

      // Get user's profile to check subscription status
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profileError) {
        return { limits: null, error: new Error(profileError.message) };
      }

      // Get active subscription if exists
      const { data: subscription, error: subError } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:subscription_plans(*)
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      let limits: UsageLimits;

      if (subscription && subscription.plan) {
        // Use subscription plan limits
        limits = subscription.plan.limits as UsageLimits;
      } else {
        // Use default free plan limits
        const { data: freePlan, error: planError } = await supabase
          .from('subscription_plans')
          .select('limits')
          .eq('name', 'Free')
          .single();

        if (planError || !freePlan) {
          // Fallback to hardcoded free limits
          limits = {
            monthly_prompts: 50,
            max_projects: 10,
            api_calls_per_day: 100,
          };
        } else {
          limits = freePlan.limits as UsageLimits;
        }
      }

      return { limits, error: null };
    } catch (error) {
      console.error('Error fetching user limits:', error);
      return { limits: null, error: error as Error };
    }
  }

  /**
   * Check if user can perform an action based on limits
   */
  static async canPerformAction(action: 'prompt' | 'project' | 'api_call'): Promise<{ allowed: boolean; error: Error | null }> {
    try {
      const [usageResult, limitsResult] = await Promise.all([
        this.getCurrentUsage(),
        this.getUserLimits(),
      ]);

      if (usageResult.error) {
        return { allowed: false, error: usageResult.error };
      }

      if (limitsResult.error) {
        return { allowed: false, error: limitsResult.error };
      }

      const usage = usageResult.usage!;
      const limits = limitsResult.limits!;

      switch (action) {
        case 'prompt':
          if (limits.monthly_prompts === -1) return { allowed: true, error: null };
          return { allowed: usage.prompts_generated < limits.monthly_prompts, error: null };
        
        case 'project':
          if (limits.max_projects === -1) return { allowed: true, error: null };
          // Get current project count
          const { data: { user } } = await supabase.auth.getUser();
          const { count } = await supabase
            .from('projects')
            .select('*', { count: 'exact', head: true })
            .eq('user_id', user!.id);
          return { allowed: (count || 0) < limits.max_projects, error: null };
        
        case 'api_call':
          if (limits.api_calls_per_day === -1) return { allowed: true, error: null };
          // For simplicity, we'll use monthly API calls instead of daily
          return { allowed: usage.api_calls < (limits.api_calls_per_day * 30), error: null };
        
        default:
          return { allowed: false, error: new Error('Invalid action') };
      }
    } catch (error) {
      console.error('Error checking action permission:', error);
      return { allowed: false, error: error as Error };
    }
  }

  /**
   * Increment usage counter
   */
  static async incrementUsage(action: 'prompt' | 'project' | 'api_call', amount: number = 1): Promise<{ success: boolean; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { success: false, error: new Error('User not authenticated') };
      }

      const currentMonth = new Date().toISOString().slice(0, 7);

      // Get or create current usage record
      const { usage, error: usageError } = await this.getCurrentUsage();
      
      if (usageError || !usage) {
        return { success: false, error: usageError || new Error('Failed to get usage') };
      }

      // Prepare update data
      const updateData: Partial<UserUsage> = {};
      
      switch (action) {
        case 'prompt':
          updateData.prompts_generated = usage.prompts_generated + amount;
          break;
        case 'project':
          updateData.projects_created = usage.projects_created + amount;
          break;
        case 'api_call':
          updateData.api_calls = usage.api_calls + amount;
          break;
      }

      // Update usage
      const { error: updateError } = await supabase
        .from('user_usage')
        .update(updateData)
        .eq('user_id', user.id)
        .eq('month_year', currentMonth);

      if (updateError) {
        return { success: false, error: new Error(updateError.message) };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Error incrementing usage:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * Get usage statistics for admin dashboard
   */
  static async getUsageStats(): Promise<{ stats: any; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { stats: null, error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { stats: null, error: new Error('Admin access required') };
      }

      // Get aggregated usage stats
      const currentMonth = new Date().toISOString().slice(0, 7);
      
      const { data: stats, error } = await supabase
        .from('user_usage')
        .select('prompts_generated, projects_created, api_calls')
        .eq('month_year', currentMonth);

      if (error) {
        return { stats: null, error: new Error(error.message) };
      }

      // Calculate totals
      const totals = stats?.reduce(
        (acc, curr) => ({
          total_prompts: acc.total_prompts + curr.prompts_generated,
          total_projects: acc.total_projects + curr.projects_created,
          total_api_calls: acc.total_api_calls + curr.api_calls,
        }),
        { total_prompts: 0, total_projects: 0, total_api_calls: 0 }
      );

      // Get user counts (all users, not just current user)
      const { count: totalUsers } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      // Get active users (users who generated prompts this month)
      const { data: activeUsersData } = await supabase
        .from('user_usage')
        .select('user_id')
        .eq('month_year', currentMonth)
        .gt('prompts_generated', 0);

      const activeUsers = activeUsersData?.length || 0;

      // Get subscription counts
      const { count: premiumUsers } = await supabase
        .from('user_subscriptions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');

      // Get revenue (sum of all successful payments this month)
      const monthStart = new Date(currentMonth + '-01').toISOString();
      const monthEnd = new Date(new Date(monthStart).getFullYear(), new Date(monthStart).getMonth() + 1, 0).toISOString();

      const { data: payments } = await supabase
        .from('payment_history')
        .select('amount')
        .eq('status', 'succeeded')
        .gte('created_at', monthStart)
        .lte('created_at', monthEnd);

      const monthlyRevenue = payments?.reduce((sum, payment) => sum + parseFloat(payment.amount.toString()), 0) || 0;

      return {
        stats: {
          ...totals,
          total_users: totalUsers || 0,
          active_users: activeUsers,
          premium_users: premiumUsers || 0,
          monthly_revenue: monthlyRevenue,
        },
        error: null,
      };
    } catch (error) {
      console.error('Error fetching usage stats:', error);
      return { stats: null, error: error as Error };
    }
  }
}
