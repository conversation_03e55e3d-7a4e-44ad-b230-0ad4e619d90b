import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Trash2, Plus, Search, Users, FolderOpen } from 'lucide-react';
import { toast } from 'sonner';
import { ProjectAccessService, ProjectAccess, GrantAccessData } from '@/lib/projectAccess';
import { ProjectsService } from '@/lib/projects';
import { supabase, Project, Profile } from '@/lib/supabase';

interface ProjectAccessManagerProps {
  className?: string;
}

const ProjectAccessManager: React.FC<ProjectAccessManagerProps> = ({ className }) => {
  const [accessList, setAccessList] = useState<ProjectAccess[]>([]);
  const [filteredAccessList, setFilteredAccessList] = useState<ProjectAccess[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [users, setUsers] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProject, setSelectedProject] = useState<string>('all');
  const [showGrantDialog, setShowGrantDialog] = useState(false);
  const [grantForm, setGrantForm] = useState<GrantAccessData>({
    projectId: 'none',
    userId: 'none',
    accessType: 'view',
  });
  const [granting, setGranting] = useState(false);

  useEffect(() => {
    loadData();
  }, [selectedProject]);

  // Client-side filtering
  useEffect(() => {
    let filtered = accessList;

    // Apply search filter
    if (searchTerm.trim()) {
      filtered = filtered.filter(access =>
        access.project?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        access.user?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        access.user?.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredAccessList(filtered);
  }, [accessList, searchTerm]);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load access list
      const { accessList: accessData, error: accessError } = await ProjectAccessService.getProjectAccessList({
        projectId: selectedProject && selectedProject !== 'all' ? selectedProject : undefined,
        limit: 100,
      });

      if (accessError) {
        toast.error('Failed to load access list');
      } else {
        setAccessList(accessData);
      }

      // Load projects for filter dropdown
      const { data: projectsData } = await supabase
        .from('projects')
        .select('*')
        .order('name');
      setProjects(projectsData || []);

      // Load users for grant access dialog
      const { data: usersData } = await supabase
        .from('profiles')
        .select('*')
        .neq('subscription_status', 'admin')
        .order('full_name');
      setUsers(usersData || []);

    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleGrantAccess = async () => {
    if (!grantForm.projectId || !grantForm.userId || grantForm.projectId === 'none' || grantForm.userId === 'none') {
      toast.error('Please select both project and user');
      return;
    }

    setGranting(true);
    try {
      const { access, error } = await ProjectAccessService.grantProjectAccess(grantForm);

      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Access granted successfully');
        setShowGrantDialog(false);
        setGrantForm({ projectId: 'none', userId: 'none', accessType: 'view' });
        loadData();
      }
    } catch (error) {
      console.error('Error granting access:', error);
      toast.error('Failed to grant access');
    } finally {
      setGranting(false);
    }
  };

  const handleRevokeAccess = async (projectId: string, userId: string, userName: string, projectTitle: string) => {
    if (!confirm(`Are you sure you want to revoke ${userName}'s access to "${projectTitle}"?`)) {
      return;
    }

    try {
      const { success, error } = await ProjectAccessService.revokeProjectAccess(projectId, userId);

      if (error) {
        toast.error(error.message);
      } else {
        toast.success('Access revoked successfully');
        loadData();
      }
    } catch (error) {
      console.error('Error revoking access:', error);
      toast.error('Failed to revoke access');
    }
  };

  const getAccessTypeBadge = (accessType: string) => {
    const variants = {
      view: 'secondary',
      edit: 'default',
      admin: 'destructive',
    } as const;

    return (
      <Badge variant={variants[accessType as keyof typeof variants] || 'secondary'}>
        {accessType}
      </Badge>
    );
  };

  const isExpired = (expiresAt: string | null) => {
    return expiresAt && new Date(expiresAt) < new Date();
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Project Access Management
          </CardTitle>
          <CardDescription>
            Grant and manage user access to specific themed projects
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Controls */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search users or projects..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={selectedProject} onValueChange={setSelectedProject}>
              <SelectTrigger className="w-full sm:w-[200px]">
                <SelectValue placeholder="Filter by project" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All projects</SelectItem>
                {projects.map((project) => (
                  <SelectItem key={project.id} value={project.id}>
                    {project.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Dialog open={showGrantDialog} onOpenChange={setShowGrantDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Grant Access
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Grant Project Access</DialogTitle>
                  <DialogDescription>
                    Give a user access to a specific project
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="project">Project</Label>
                    <Select value={grantForm.projectId} onValueChange={(value) => setGrantForm(prev => ({ ...prev, projectId: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select project" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none" disabled>Select a project</SelectItem>
                        {projects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            <div className="flex items-center gap-2">
                              <FolderOpen className="h-4 w-4" />
                              <span>{project.name}</span>
                              {project.theme_category && (
                                <Badge variant="outline" className="text-xs">
                                  {project.theme_category}
                                </Badge>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="user">User</Label>
                    <Select value={grantForm.userId} onValueChange={(value) => setGrantForm(prev => ({ ...prev, userId: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select user" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none" disabled>Select a user</SelectItem>
                        {users.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            <div className="flex flex-col">
                              <span>{user.full_name || 'Anonymous'}</span>
                              <span className="text-xs text-muted-foreground">{user.email}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="accessType">Access Type</Label>
                    <Select value={grantForm.accessType} onValueChange={(value) => setGrantForm(prev => ({ ...prev, accessType: value as 'view' | 'edit' | 'admin' }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="view">View Only</SelectItem>
                        <SelectItem value="edit">Edit Access</SelectItem>
                        <SelectItem value="admin">Admin Access</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="expiresAt">Expires At (Optional)</Label>
                    <Input
                      type="datetime-local"
                      value={grantForm.expiresAt || ''}
                      onChange={(e) => setGrantForm(prev => ({ ...prev, expiresAt: e.target.value || undefined }))}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowGrantDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleGrantAccess} disabled={granting}>
                    {granting ? 'Granting...' : 'Grant Access'}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Access List */}
          <div className="space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground mt-2">Loading access list...</p>
              </div>
            ) : filteredAccessList.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No project access grants found</p>
                <p className="text-sm text-muted-foreground">Grant users access to specific projects to get started</p>
              </div>
            ) : (
              filteredAccessList.map((access) => (
                <Card key={access.id} className={`${isExpired(access.expires_at) ? 'opacity-60' : ''}`}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <div>
                            <h4 className="font-medium">{access.user?.full_name || 'Anonymous'}</h4>
                            <p className="text-sm text-muted-foreground">{access.user?.email}</p>
                          </div>
                          <div className="text-muted-foreground">→</div>
                          <div>
                            <h4 className="font-medium">{access.project?.name}</h4>
                            {access.project?.theme_category && (
                              <Badge variant="outline" className="text-xs">
                                {access.project.theme_category}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          {getAccessTypeBadge(access.access_type)}
                          <span>•</span>
                          <span>Granted {new Date(access.created_at).toLocaleDateString()}</span>
                          {access.expires_at && (
                            <>
                              <span>•</span>
                              <span className={isExpired(access.expires_at) ? 'text-red-500' : ''}>
                                {isExpired(access.expires_at) ? 'Expired' : 'Expires'} {new Date(access.expires_at).toLocaleDateString()}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRevokeAccess(
                          access.project_id,
                          access.user_id,
                          access.user?.full_name || 'Anonymous',
                          access.project?.name || 'Unknown Project'
                        )}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProjectAccessManager;
