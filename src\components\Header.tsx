import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ExternalLink, User, LogOut, FolderOpen, BarChart3, CreditCard, Shield } from 'lucide-react';
import { Link } from 'react-router-dom';
import Logo from '@/components/Logo';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user, profile, signOut } = useAuth();

  return (
    <header className="flex justify-between items-center p-6">
      <Link to="/">
        <Logo />
      </Link>

      <div className="flex items-center gap-3">
        <Link to="/docs" className="flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-white/90 hover:shadow-md transition-all duration-300">
          <ExternalLink className="w-4 h-4" />
          Learn More
        </Link>

        <Link to="/pricing" className="flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-white/90 hover:shadow-md transition-all duration-300">
          <CreditCard className="w-4 h-4" />
          Pricing
        </Link>

        {isAuthenticated && (profile?.subscription_status === 'admin' || user?.email === '<EMAIL>') && (
          <Link to="/admin" className="flex items-center gap-2 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-full px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md hover:from-red-600 hover:to-pink-700 transition-all duration-300">
            <Shield className="w-4 h-4" />
            Admin
          </Link>
        )}

        {!isAuthenticated ? (
          <Link to="/auth" className="flex items-center gap-2 bg-gradient-to-r from-highlight to-blue-500 text-white rounded-full px-4 py-2 text-sm font-medium shadow-sm hover:shadow-md transition-all duration-300">
            <User className="w-4 h-4" />
            Sign In
          </Link>
        ) : (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="bg-white/80 backdrop-blur-sm border border-gray-200 rounded-full px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-white/90 hover:shadow-md transition-all duration-300">
                <User className="w-4 h-4 mr-2" />
                {profile?.full_name || user?.email}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuItem onClick={() => navigate('/dashboard')}>
                <BarChart3 className="w-4 h-4 mr-2" />
                Dashboard
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigate('/projects')}>
                <FolderOpen className="w-4 h-4 mr-2" />
                My Projects
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={signOut}>
                <LogOut className="w-4 h-4 mr-2" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
};

export default Header;


