<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6"/>
      <stop offset="100%" style="stop-color:#60A5FA"/>
    </linearGradient>
    <linearGradient id="ringGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60A5FA"/>
      <stop offset="100%" style="stop-color:#93C5FD"/>
    </linearGradient>
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="1.5"/>
      <feOffset dx="0" dy="0"/>
      <feComposite in2="SourceGraphic" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.376   0 0 0 0 0.51   0 0 0 0 0.969  0 0 0 0.5 0"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Main circle (planet) -->
  <circle cx="16" cy="16" r="14" fill="url(#bgGradient)" filter="url(#glow)"/>

  <!-- Orbital ring -->
  <ellipse cx="16" cy="16" rx="12" ry="6" stroke="url(#ringGradient)" stroke-width="0.75" fill="none" transform="rotate(30, 16, 16)" opacity="0.7"/>

  <!-- Stars -->
  <circle cx="8" cy="10" r="0.75" fill="white" opacity="0.8"/>
  <circle cx="24" cy="8" r="0.5" fill="white" opacity="0.6"/>
  <circle cx="22" cy="22" r="0.6" fill="white" opacity="0.7"/>
  <circle cx="6" cy="18" r="0.4" fill="white" opacity="0.5"/>

  <!-- Stylized "P" -->
  <path d="M13 10C13 9.44772 13.4477 9 14 9H18C19.6569 9 21 10.3431 21 12C21 13.6569 19.6569 15 18 15H14V21" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>