export const getGroqConfig = () => {
  // Prioritize environment variables over localStorage
  return {
    apiKey: import.meta.env.VITE_GROQ_API_KEY || localStorage.getItem('groq_api_key') || '',
    model: import.meta.env.VITE_GROQ_MODEL || localStorage.getItem('groq_model') || 'mixtral-8x7b-32768'
  };
};

export const isGroqConfigured = () => {
  const { apiKey } = getGroqConfig();
  return !!apiKey;
};

export const saveGroqConfig = (apiKey: string, model: string) => {
  // In development, we can update localStorage
  if (import.meta.env.DEV) {
    localStorage.setItem('groq_api_key', apiKey);
    localStorage.setItem('groq_model', model);
    return true;
  }
  
  // In production, warn that env vars should be used
  console.warn('In production, GROQ configuration should be set via environment variables.');
  return false;
};
