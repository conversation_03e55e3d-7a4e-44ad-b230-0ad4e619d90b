import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.0.0'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') || '', {
  apiVersion: '2023-10-16',
})

const supabaseClient = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

// Custom webhook signature verification for Deno
async function verifyWebhookSignature(body: string, signature: string, secret: string): Promise<boolean> {
  try {
    const elements = signature.split(',')
    const signatureElements: { [key: string]: string } = {}
    
    for (const element of elements) {
      const [key, value] = element.split('=')
      signatureElements[key] = value
    }
    
    const timestamp = signatureElements.t
    const v1 = signatureElements.v1
    
    if (!timestamp || !v1) {
      return false
    }
    
    const payload = timestamp + '.' + body
    const encoder = new TextEncoder()
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(secret),
      { name: 'HMAC', hash: 'SHA-256' },
      false,
      ['sign']
    )
    
    const signature_bytes = await crypto.subtle.sign('HMAC', key, encoder.encode(payload))
    const expected_signature = Array.from(new Uint8Array(signature_bytes))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
    
    return expected_signature === v1
  } catch (error) {
    console.error('Signature verification error:', error)
    return false
  }
}

serve(async (req) => {
  const signature = req.headers.get('stripe-signature')
  const body = await req.text()
  const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SECRET')

  if (!signature || !webhookSecret) {
    return new Response('Missing signature or webhook secret', { status: 400 })
  }

  // Use custom verification instead of Stripe's built-in method
  const isValid = await verifyWebhookSignature(body, signature, webhookSecret)
  
  if (!isValid) {
    console.error('Webhook signature verification failed')
    return new Response('Webhook signature verification failed', { status: 400 })
  }

  let event: Stripe.Event

  try {
    event = JSON.parse(body)
  } catch (err) {
    console.error('Error parsing webhook body:', err)
    return new Response('Invalid JSON', { status: 400 })
  }

  console.log('Received webhook event:', event.type)

  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        console.log('Checkout session completed:', session.id)

        if (session.mode === 'subscription' && session.subscription) {
          const subscription = await stripe.subscriptions.retrieve(session.subscription as string)
          const customerId = session.customer as string
          const planId = session.metadata?.planId

          // Get the user ID from customer metadata
          const customer = await stripe.customers.retrieve(customerId)
          const supabaseUserId = (customer as Stripe.Customer).metadata?.supabase_user_id

          if (supabaseUserId && planId) {
            // Create subscription record with proper timestamp handling
            const currentPeriodStart = subscription.current_period_start 
              ? new Date(subscription.current_period_start * 1000).toISOString()
              : new Date().toISOString();
            
            const currentPeriodEnd = subscription.current_period_end
              ? new Date(subscription.current_period_end * 1000).toISOString()
              : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // Default to 30 days from now

            await supabaseClient
              .from('user_subscriptions')
              .insert({
                user_id: supabaseUserId,
                plan_id: planId,
                stripe_customer_id: customerId,
                stripe_subscription_id: subscription.id,
                status: subscription.status,
                current_period_start: currentPeriodStart,
                current_period_end: currentPeriodEnd,
              })

            // Update user's subscription status
            await supabaseClient
              .from('profiles')
              .update({
                subscription_status: 'premium'
              })
              .eq('id', supabaseUserId)

            console.log('Subscription created for user:', supabaseUserId)
          }
        }
        break
      }

      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription
        console.log('Subscription updated:', subscription.id)

        // Get user from subscription first
        const { data: userSub } = await supabaseClient
          .from('user_subscriptions')
          .select('user_id')
          .eq('stripe_subscription_id', subscription.id)
          .single()

        if (userSub) {
          // Update subscription record with proper timestamp handling
          const currentPeriodStart = subscription.current_period_start 
            ? new Date(subscription.current_period_start * 1000).toISOString()
            : new Date().toISOString();
          
          const currentPeriodEnd = subscription.current_period_end
            ? new Date(subscription.current_period_end * 1000).toISOString()
            : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();

          await supabaseClient
            .from('user_subscriptions')
            .update({
              status: subscription.status,
              current_period_start: currentPeriodStart,
              current_period_end: currentPeriodEnd,
              cancel_at_period_end: subscription.cancel_at_period_end,
            })
            .eq('stripe_subscription_id', subscription.id)

          // Update profile status based on subscription status
          const profileStatus = subscription.status === 'active' ? 'premium' : 'free'
          await supabaseClient
            .from('profiles')
            .update({ subscription_status: profileStatus })
            .eq('id', userSub.user_id)

          console.log('Subscription updated for user:', userSub.user_id)
        }
        break
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription
        console.log('Subscription deleted:', subscription.id)

        // Get user from subscription
        const { data: userSub } = await supabaseClient
          .from('user_subscriptions')
          .select('user_id')
          .eq('stripe_subscription_id', subscription.id)
          .single()

        if (userSub) {
          // Update subscription status
          await supabaseClient
            .from('user_subscriptions')
            .update({ status: 'canceled' })
            .eq('stripe_subscription_id', subscription.id)

          // Update user's subscription status back to free
          await supabaseClient
            .from('profiles')
            .update({ subscription_status: 'free' })
            .eq('id', userSub.user_id)

          console.log('Subscription canceled for user:', userSub.user_id)
        }
        break
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice
        console.log('Payment succeeded:', invoice.id)

        if (invoice.customer && invoice.subscription) {
          // Get user from customer
          const customer = await stripe.customers.retrieve(invoice.customer as string)
          const supabaseUserId = (customer as Stripe.Customer).metadata?.supabase_user_id

          if (supabaseUserId) {
            // Record payment
            await supabaseClient
              .from('payment_history')
              .insert({
                user_id: supabaseUserId,
                stripe_payment_intent_id: invoice.payment_intent as string,
                amount: invoice.amount_paid / 100, // Convert from cents
                currency: invoice.currency,
                status: 'succeeded',
                description: invoice.description || 'Subscription payment',
              })

            console.log('Payment recorded for user:', supabaseUserId)
          }
        }
        break
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice
        console.log('Payment failed:', invoice.id)

        if (invoice.customer) {
          // Get user from customer
          const customer = await stripe.customers.retrieve(invoice.customer as string)
          const supabaseUserId = (customer as Stripe.Customer).metadata?.supabase_user_id

          if (supabaseUserId) {
            // Record failed payment
            await supabaseClient
              .from('payment_history')
              .insert({
                user_id: supabaseUserId,
                stripe_payment_intent_id: invoice.payment_intent as string,
                amount: invoice.amount_due / 100, // Convert from cents
                currency: invoice.currency,
                status: 'failed',
                description: invoice.description || 'Failed subscription payment',
              })

            console.log('Failed payment recorded for user:', supabaseUserId)
          }
        }
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return new Response(JSON.stringify({ received: true }), {
      headers: { 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return new Response('Webhook processing failed', { status: 500 })
  }
})






