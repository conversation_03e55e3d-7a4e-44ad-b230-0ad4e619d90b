
export interface PromptStyle {
  id: string;
  name: string;
  description: string;
  template: (idea: string) => string;
}

export const promptStyles: PromptStyle[] = [
  {
    id: 'technical',
    name: 'Technical',
    description: 'Structured, detailed instructions for technical projects',
    template: (idea: string) => {
      return `# Technical Project Requirements

## Project Overview
Create a ${idea} with the following specifications:

## Core Features
- Feature 1: [Primary functionality]
- Feature 2: [Secondary functionality]
- Feature 3: [Additional functionality]

## Technical Requirements
- Use modern, maintainable code architecture
- Implement responsive design for all device sizes
- Ensure accessibility compliance
- Optimize for performance

## Acceptance Criteria
- The solution must be fully functional and meet all core requirements
- Code should be well-documented and follow best practices
- Include comprehensive test coverage`;
    }
  },
  {
    id: 'creative',
    name: 'Creative',
    description: 'Open-ended, imaginative prompts for creative work',
    template: (idea: string) => {
      return `# Creative Project Concept

Develop a unique and innovative ${idea} that captures attention and inspires.

## Creative Direction
- Create a distinctive visual identity that stands out
- Incorporate unexpected elements that surprise and delight
- Balance aesthetic appeal with functional design

## Emotional Impact
- Evoke feelings of [emotion] in the audience
- Create moments of connection and resonance
- Leave a lasting impression that encourages return engagement

## Inspiration Sources
- Draw from [relevant industry examples]
- Incorporate elements of [cultural references]
- Put a fresh spin on traditional concepts`;
    }
  },
  {
    id: 'business',
    name: 'Business',
    description: 'Goal-oriented prompts focused on business outcomes',
    template: (idea: string) => {
      return `# Business Project Plan

## Project Objective
Develop a ${idea} that solves [specific business problem] and delivers measurable ROI.

## Target Audience
- Primary: [main demographic]
- Secondary: [additional demographic]
- Customer needs: [pain points to address]

## Business Goals
- Increase [key metric] by [target percentage]
- Reduce [pain point] by [target amount]
- Generate [specific outcome] within [timeframe]

## Success Metrics
- KPI 1: [specific measurement]
- KPI 2: [specific measurement]
- Timeline: [project milestones]`;
    }
  },
  {
    id: 'concise',
    name: 'Concise',
    description: 'Brief, to-the-point instructions with minimal detail',
    template: (idea: string) => {
      return `Build a ${idea}.

Make it:
- Simple & intuitive
- Visually appealing
- Functional & useful

Include essential features only. Focus on quality over quantity.`;
    }
  },
  {
    id: 'detailed',
    name: 'Detailed',
    description: 'Comprehensive instructions with extensive specifications',
    template: (idea: string) => {
      return `# Comprehensive Project Specification

## Project Overview and Objectives
Create a fully-featured ${idea} that addresses [specific problem] for [target users]. The solution should be comprehensive, polished, and ready for deployment.

## Detailed Requirements

### Functional Requirements
1. Core Functionality
   - Feature A: [detailed description]
   - Feature B: [detailed description]
   - Feature C: [detailed description]

2. User Experience
   - Navigation flow: [specific navigation pattern]
   - Interaction design: [interaction specifications]
   - Accessibility requirements: [WCAG compliance level]

3. Technical Architecture
   - Frontend: [specific frameworks/libraries]
   - Backend: [specific technologies]
   - Database: [data structure and relationships]
   - APIs: [necessary integrations]

### Non-Functional Requirements
- Performance metrics: [specific targets]
- Security considerations: [specific protocols]
- Scalability requirements: [expected growth accommodation]

## Implementation Guidelines
- Design system: [specific design principles]
- Code quality: [specific standards]
- Testing approach: [testing methodologies]

## Deliverables
- [Specific artifact 1]
- [Specific artifact 2]
- [Specific artifact 3]`;
    }
  }
];
