import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Project } from '@/lib/supabase';
import { useProjects } from '@/hooks/useProjects';
import { toast } from 'sonner';
import { Loader2, Edit } from 'lucide-react';

interface EditProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project: Project | null;
  onProjectUpdated?: (project: Project) => void;
}

const themeCategories = [
  { value: '', label: 'No Category' },
  { value: 'marketing', label: 'Marketing' },
  { value: 'technical', label: 'Technical' },
  { value: 'creative', label: 'Creative Writing' },
  { value: 'business', label: 'Business' },
  { value: 'education', label: 'Education' },
  { value: 'research', label: 'Research' },
  { value: 'personal', label: 'Personal' },
];

const EditProjectDialog: React.FC<EditProjectDialogProps> = ({
  open,
  onOpenChange,
  project,
  onProjectUpdated
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [themeCategory, setThemeCategory] = useState('');
  const [isPublic, setIsPublic] = useState(false);
  const [saving, setSaving] = useState(false);
  const { updateProject } = useProjects();

  useEffect(() => {
    if (project && open) {
      setTitle((project as any).title || project.name);
      setDescription(project.description || '');
      setThemeCategory((project as any).theme_category || '');
      setIsPublic(project.is_public);
    }
  }, [project, open]);

  const handleSave = async () => {
    if (!project) return;

    // Validate form
    if (!title.trim()) {
      toast.error('Project title is required');
      return;
    }

    setSaving(true);

    try {
      // Handle both old and new schema
      const updates: any = {
        description: description.trim() || null,
        is_public: isPublic
      };

      // Use appropriate field names based on schema
      if ((project as any).title !== undefined) {
        // Old schema
        updates.title = title.trim();
        updates.theme_category = themeCategory || null;
      } else {
        // New schema
        updates.name = title.trim();
      }

      const updatedProject = await updateProject(project.id, updates);
      
      if (updatedProject) {
        onProjectUpdated?.(updatedProject);
        onOpenChange(false);
        toast.success('Project updated successfully');
      }
    } catch (error) {
      console.error('Error updating project:', error);
      toast.error('Failed to update project');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    // Reset form to original values
    if (project) {
      setTitle(project.title);
      setDescription(project.description || '');
      setThemeCategory(project.theme_category || '');
      setIsPublic(project.is_public);
    }
  };

  if (!project) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="w-5 h-5" />
            Edit Project
          </DialogTitle>
          <DialogDescription>
            Update your project details and settings.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="edit-title">Project Title *</Label>
            <Input
              id="edit-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter project title..."
              maxLength={100}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-description">Description</Label>
            <Textarea
              id="edit-description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Optional description of your project..."
              rows={3}
              maxLength={500}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="edit-category">Category</Label>
            <Select value={themeCategory} onValueChange={setThemeCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Select a category..." />
              </SelectTrigger>
              <SelectContent>
                {themeCategories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="edit-public"
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
            <Label htmlFor="edit-public" className="text-sm">
              Make this project public (others can view it)
            </Label>
          </div>

          {/* Project Stats */}
          <div className="border rounded-lg p-3 bg-gray-50">
            <h4 className="font-medium text-sm text-gray-700 mb-2">Project Statistics:</h4>
            <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
              <div>
                <span className="font-medium">Created:</span>
                <br />
                {new Date(project.created_at).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium">Last Updated:</span>
                <br />
                {new Date(project.updated_at).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium">Iterations:</span>
                <br />
                {(project.content.iterations?.length || 0) + 1}
              </div>
              <div>
                <span className="font-medium">Word Count:</span>
                <br />
                {project.content.metadata?.wordCount || 0}
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={handleCancel} disabled={saving}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            Save Changes
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditProjectDialog;
