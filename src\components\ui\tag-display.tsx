import React from 'react';
import { Hash, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from './badge';
import { Button } from './button';

export interface TagDisplayProps {
  tags: string[];
  onTagClick?: (tag: string) => void;
  onTagRemove?: (tag: string) => void;
  maxVisible?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'secondary';
  showHash?: boolean;
  removable?: boolean;
  clickable?: boolean;
  className?: string;
}

export function TagDisplay({
  tags,
  onTagClick,
  onTagRemove,
  maxVisible,
  size = 'md',
  variant = 'secondary',
  showHash = true,
  removable = false,
  clickable = true,
  className
}: TagDisplayProps) {
  if (!tags || tags.length === 0) {
    return null;
  }

  const visibleTags = maxVisible ? tags.slice(0, maxVisible) : tags;
  const hiddenCount = maxVisible && tags.length > maxVisible ? tags.length - maxVisible : 0;

  const getTagColor = (tag: string): string => {
    const colors = [
      'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200',
      'bg-green-100 text-green-800 border-green-200 hover:bg-green-200',
      'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200',
      'bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200',
      'bg-pink-100 text-pink-800 border-pink-200 hover:bg-pink-200',
      'bg-indigo-100 text-indigo-800 border-indigo-200 hover:bg-indigo-200',
      'bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200',
      'bg-cyan-100 text-cyan-800 border-cyan-200 hover:bg-cyan-200',
    ];
    
    const hash = tag.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  const getSizeClasses = (size: string): string => {
    switch (size) {
      case 'sm':
        return 'text-xs px-2 py-1 h-5';
      case 'lg':
        return 'text-sm px-3 py-1.5 h-7';
      default:
        return 'text-xs px-2.5 py-1 h-6';
    }
  };

  const handleTagClick = (tag: string) => {
    if (clickable && onTagClick) {
      onTagClick(tag);
    }
  };

  const handleTagRemove = (e: React.MouseEvent, tag: string) => {
    e.stopPropagation();
    if (onTagRemove) {
      onTagRemove(tag);
    }
  };

  return (
    <div className={cn("flex flex-wrap gap-1", className)}>
      {visibleTags.map((tag) => (
        <Badge
          key={tag}
          variant={variant}
          className={cn(
            "font-medium border transition-colors",
            getSizeClasses(size),
            getTagColor(tag),
            clickable && onTagClick && "cursor-pointer hover:shadow-sm",
            removable && "pr-1"
          )}
          onClick={() => handleTagClick(tag)}
        >
          <div className="flex items-center gap-1">
            {showHash && <Hash className="w-3 h-3" />}
            <span className="truncate max-w-[100px]">{tag}</span>
            {removable && onTagRemove && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="ml-1 h-auto p-0 text-current hover:bg-transparent opacity-70 hover:opacity-100"
                onClick={(e) => handleTagRemove(e, tag)}
              >
                <X className="w-3 h-3" />
              </Button>
            )}
          </div>
        </Badge>
      ))}
      
      {hiddenCount > 0 && (
        <Badge
          variant="outline"
          className={cn(
            "font-medium text-muted-foreground",
            getSizeClasses(size)
          )}
        >
          +{hiddenCount} more
        </Badge>
      )}
    </div>
  );
}

export interface TagListProps {
  tags: string[];
  title?: string;
  onTagClick?: (tag: string) => void;
  onTagRemove?: (tag: string) => void;
  removable?: boolean;
  className?: string;
}

export function TagList({
  tags,
  title,
  onTagClick,
  onTagRemove,
  removable = false,
  className
}: TagListProps) {
  if (!tags || tags.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-2", className)}>
      {title && (
        <h4 className="text-sm font-medium text-muted-foreground">{title}</h4>
      )}
      <TagDisplay
        tags={tags}
        onTagClick={onTagClick}
        onTagRemove={onTagRemove}
        removable={removable}
        size="sm"
      />
    </div>
  );
}

export interface TagCloudProps {
  tags: Array<{
    name: string;
    count: number;
  }>;
  onTagClick?: (tag: string) => void;
  maxTags?: number;
  className?: string;
}

export function TagCloud({
  tags,
  onTagClick,
  maxTags = 50,
  className
}: TagCloudProps) {
  if (!tags || tags.length === 0) {
    return null;
  }

  // Sort by count and limit
  const sortedTags = tags
    .sort((a, b) => b.count - a.count)
    .slice(0, maxTags);

  const maxCount = Math.max(...sortedTags.map(t => t.count));
  const minCount = Math.min(...sortedTags.map(t => t.count));

  const getTagSize = (count: number): string => {
    const ratio = maxCount > minCount ? (count - minCount) / (maxCount - minCount) : 0.5;
    
    if (ratio > 0.8) return 'text-lg font-bold';
    if (ratio > 0.6) return 'text-base font-semibold';
    if (ratio > 0.4) return 'text-sm font-medium';
    return 'text-xs font-normal';
  };

  const getTagOpacity = (count: number): string => {
    const ratio = maxCount > minCount ? (count - minCount) / (maxCount - minCount) : 0.5;
    return ratio > 0.5 ? 'opacity-100' : 'opacity-70';
  };

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {sortedTags.map(({ name, count }) => (
        <button
          key={name}
          type="button"
          className={cn(
            "inline-flex items-center gap-1 px-2 py-1 rounded-md",
            "bg-secondary/50 hover:bg-secondary text-secondary-foreground",
            "transition-all duration-200 hover:scale-105",
            getTagSize(count),
            getTagOpacity(count),
            onTagClick && "cursor-pointer"
          )}
          onClick={() => onTagClick?.(name)}
        >
          <Hash className="w-3 h-3" />
          {name}
          <span className="text-xs opacity-60 ml-1">({count})</span>
        </button>
      ))}
    </div>
  );
}
