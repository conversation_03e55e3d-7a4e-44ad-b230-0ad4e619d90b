import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription, usePremiumFeatures } from '@/hooks/useSubscription';
import BackgroundEffects from '@/components/BackgroundEffects';
import AccessibleProjects from '@/components/AccessibleProjects';
import { 
  FolderOpen, 
  Crown, 
  Activity, 
  TrendingUp, 
  Calendar,
  LogOut,
  User,
  CreditCard
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const MemberDashboard = () => {
  const { user, profile, signOut } = useAuth();
  const { subscription, usage, loading } = useSubscription();
  const { hasPremiumAccess, hasAdminAccess } = usePremiumFeatures();
  const navigate = useNavigate();

  if (loading) {
    return (
      <div className="min-h-screen relative overflow-hidden flex items-center justify-center">
        <BackgroundEffects />
        <div className="relative z-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground mt-2">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  const getSubscriptionBadge = () => {
    if (hasAdminAccess) {
      return <Badge variant="destructive" className="ml-2">Admin</Badge>;
    }
    if (hasPremiumAccess) {
      return <Badge variant="default" className="ml-2">Premium</Badge>;
    }
    return <Badge variant="secondary" className="ml-2">Free</Badge>;
  };

  const getUsagePercentage = (used: number, limit: number) => {
    if (limit === -1) return 0; // Unlimited
    return Math.min((used / limit) * 100, 100);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundEffects />
      <div className="relative z-10">
        {/* Header */}
        <header className="border-b bg-white/80 backdrop-blur-sm">
          <div className="container max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h1 className="text-2xl font-bold">Member Dashboard</h1>
                {getSubscriptionBadge()}
              </div>
              <div className="flex items-center gap-4">
                <div className="text-sm text-muted-foreground">
                  Welcome, {profile?.full_name || user?.email || 'Member'}
                </div>
                <Button
                  variant="outline"
                  onClick={signOut}
                  className="flex items-center gap-2"
                >
                  <LogOut className="w-4 h-4" />
                  Logout
                </Button>
              </div>
            </div>
          </div>
        </header>

        <main className="container max-w-7xl mx-auto px-6 py-8">
          <Tabs defaultValue="projects" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="projects" className="flex items-center gap-2">
                <FolderOpen className="w-4 h-4" />
                My Projects
              </TabsTrigger>
              <TabsTrigger value="usage" className="flex items-center gap-2">
                <Activity className="w-4 h-4" />
                Usage
              </TabsTrigger>
              <TabsTrigger value="account" className="flex items-center gap-2">
                <User className="w-4 h-4" />
                Account
              </TabsTrigger>
            </TabsList>

            <TabsContent value="projects" className="space-y-6">
              <AccessibleProjects />
            </TabsContent>

            <TabsContent value="usage" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Prompts Generated</CardTitle>
                    <Activity className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{usage?.prompts_generated || 0}</div>
                    <div className="text-xs text-muted-foreground">
                      {subscription?.plan?.limits?.monthly_prompts === -1 
                        ? 'Unlimited' 
                        : `of ${subscription?.plan?.limits?.monthly_prompts || 50} this month`
                      }
                    </div>
                    {subscription?.plan?.limits?.monthly_prompts !== -1 && (
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ 
                            width: `${getUsagePercentage(
                              usage?.prompts_generated || 0, 
                              subscription?.plan?.limits?.monthly_prompts || 50
                            )}%` 
                          }}
                        ></div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Projects Created</CardTitle>
                    <FolderOpen className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{usage?.projects_created || 0}</div>
                    <div className="text-xs text-muted-foreground">
                      {subscription?.plan?.limits?.max_projects === -1 
                        ? 'Unlimited' 
                        : `of ${subscription?.plan?.limits?.max_projects || 10} total`
                      }
                    </div>
                    {subscription?.plan?.limits?.max_projects !== -1 && (
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ 
                            width: `${getUsagePercentage(
                              usage?.projects_created || 0, 
                              subscription?.plan?.limits?.max_projects || 10
                            )}%` 
                          }}
                        ></div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">API Calls</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{usage?.api_calls || 0}</div>
                    <div className="text-xs text-muted-foreground">
                      {subscription?.plan?.limits?.api_calls_per_day === -1 
                        ? 'Unlimited' 
                        : `of ${(subscription?.plan?.limits?.api_calls_per_day || 100) * 30} this month`
                      }
                    </div>
                    {subscription?.plan?.limits?.api_calls_per_day !== -1 && (
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ 
                            width: `${getUsagePercentage(
                              usage?.api_calls || 0, 
                              (subscription?.plan?.limits?.api_calls_per_day || 100) * 30
                            )}%` 
                          }}
                        ></div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {!hasPremiumAccess && (
                <Card className="border-yellow-200 bg-yellow-50">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-yellow-800">
                      <Crown className="h-5 w-5" />
                      Upgrade to Premium
                    </CardTitle>
                    <CardDescription className="text-yellow-700">
                      Get unlimited prompts, access to premium templates, and more!
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button 
                      onClick={() => navigate('/pricing')}
                      className="bg-yellow-600 hover:bg-yellow-700 text-white"
                    >
                      View Pricing Plans
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="account" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                    <CardDescription>Your account details</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Full Name</label>
                      <p className="text-sm">{profile?.full_name || 'Not set'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Email</label>
                      <p className="text-sm">{user?.email}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Member Since</label>
                      <p className="text-sm">
                        {profile?.created_at ? new Date(profile.created_at).toLocaleDateString() : 'Unknown'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Subscription Status</label>
                      <div className="flex items-center gap-2">
                        <p className="text-sm">{profile?.subscription_status || 'free'}</p>
                        {getSubscriptionBadge()}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      Subscription
                    </CardTitle>
                    <CardDescription>Manage your subscription</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {subscription ? (
                      <div className="space-y-2">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Current Plan</label>
                          <p className="text-sm font-medium">{subscription.plan?.name}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Status</label>
                          <p className="text-sm">{subscription.status}</p>
                        </div>
                        {subscription.current_period_end && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Next Billing</label>
                            <p className="text-sm">
                              {new Date(subscription.current_period_end).toLocaleDateString()}
                            </p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-sm text-muted-foreground mb-4">No active subscription</p>
                        <Button onClick={() => navigate('/pricing')}>
                          View Plans
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>
    </div>
  );
};

export default MemberDashboard;
