
import React from 'react';

interface LogoProps {
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ className = '' }) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="w-8 h-8 bg-gradient-to-tr from-highlight to-blue-400 rounded-full shadow-md flex items-center justify-center">
        <span className="text-white font-bold text-xs">C</span>
      </div>
      <span className="text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-gray-800 to-gray-600">
        ChatsPrompt
      </span>
    </div>
  );
};

export default Logo;
