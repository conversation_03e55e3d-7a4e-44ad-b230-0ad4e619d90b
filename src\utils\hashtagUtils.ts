/**
 * Utility functions for parsing and managing hashtags in text
 */

export interface ParsedHashtags {
  hashtags: string[];
  cleanText: string;
  originalText: string;
}

export interface TagSuggestion {
  tag: string;
  confidence: number;
  source: 'hashtag' | 'keyword' | 'category';
}

/**
 * Extract hashtags from text
 * Matches patterns like #marketing, #ai-generated, #web_development
 */
export function extractHashtags(text: string): string[] {
  if (!text) return [];
  
  const hashtagRegex = /#([a-zA-Z0-9_-]+)/g;
  const matches = text.match(hashtagRegex);
  
  if (!matches) return [];
  
  // Remove # and convert to lowercase, remove duplicates
  return [...new Set(matches.map(tag => tag.slice(1).toLowerCase()))];
}

/**
 * Parse text to extract hashtags and return clean text
 */
export function parseHashtags(text: string): ParsedHashtags {
  const hashtags = extractHashtags(text);
  const cleanText = text.replace(/#([a-zA-Z0-9_-]+)/g, '').trim();
  
  return {
    hashtags,
    cleanText,
    originalText: text
  };
}

/**
 * Remove hashtags from text while preserving formatting
 */
export function removeHashtags(text: string): string {
  return text.replace(/#([a-zA-Z0-9_-]+)/g, '').replace(/\s+/g, ' ').trim();
}

/**
 * Add hashtags to text if they don't already exist
 */
export function addHashtags(text: string, hashtags: string[]): string {
  const existingHashtags = extractHashtags(text);
  const newHashtags = hashtags.filter(tag => 
    !existingHashtags.includes(tag.toLowerCase())
  );
  
  if (newHashtags.length === 0) return text;
  
  const hashtagString = newHashtags.map(tag => `#${tag}`).join(' ');
  return `${text} ${hashtagString}`.trim();
}

/**
 * Validate hashtag format
 */
export function isValidHashtag(hashtag: string): boolean {
  // Remove # if present
  const cleanTag = hashtag.startsWith('#') ? hashtag.slice(1) : hashtag;
  
  // Check format: letters, numbers, hyphens, underscores only
  // Must start with letter or number, 1-50 characters
  const hashtagRegex = /^[a-zA-Z0-9][a-zA-Z0-9_-]{0,49}$/;
  return hashtagRegex.test(cleanTag);
}

/**
 * Normalize hashtag (remove #, lowercase, validate)
 */
export function normalizeHashtag(hashtag: string): string | null {
  const cleanTag = hashtag.startsWith('#') ? hashtag.slice(1) : hashtag;
  const normalized = cleanTag.toLowerCase().trim();
  
  return isValidHashtag(normalized) ? normalized : null;
}

/**
 * Suggest tags based on text content using keyword analysis
 */
export function suggestTagsFromContent(text: string, existingTags: string[] = []): TagSuggestion[] {
  if (!text) return [];
  
  const suggestions: TagSuggestion[] = [];
  const words = text.toLowerCase().split(/\s+/);
  
  // Common keyword to tag mappings
  const keywordMappings: Record<string, string[]> = {
    'marketing': ['marketing', 'advertising', 'promotion'],
    'technical': ['technical', 'programming', 'development', 'code', 'software'],
    'business': ['business', 'corporate', 'professional', 'company'],
    'creative': ['creative', 'design', 'art', 'writing', 'content'],
    'education': ['education', 'learning', 'teaching', 'training', 'course'],
    'research': ['research', 'analysis', 'study', 'investigation'],
    'social': ['social', 'media', 'facebook', 'twitter', 'instagram', 'linkedin'],
    'email': ['email', 'newsletter', 'campaign', 'mailchimp'],
    'seo': ['seo', 'search', 'optimization', 'google', 'ranking'],
    'ecommerce': ['ecommerce', 'shop', 'store', 'product', 'sales', 'commerce'],
    'ai': ['ai', 'artificial', 'intelligence', 'machine', 'learning', 'chatgpt'],
    'web': ['web', 'website', 'online', 'internet', 'digital'],
    'mobile': ['mobile', 'app', 'ios', 'android', 'smartphone'],
    'urgent': ['urgent', 'asap', 'priority', 'important', 'deadline'],
    'draft': ['draft', 'wip', 'progress', 'unfinished', 'todo']
  };
  
  // Check for keyword matches
  Object.entries(keywordMappings).forEach(([tag, keywords]) => {
    if (existingTags.includes(tag)) return;
    
    const matches = keywords.filter(keyword => 
      words.some(word => word.includes(keyword))
    );
    
    if (matches.length > 0) {
      suggestions.push({
        tag,
        confidence: Math.min(matches.length * 0.3, 1),
        source: 'keyword'
      });
    }
  });
  
  // Extract hashtags as high-confidence suggestions
  const hashtags = extractHashtags(text);
  hashtags.forEach(hashtag => {
    if (!existingTags.includes(hashtag)) {
      suggestions.push({
        tag: hashtag,
        confidence: 1,
        source: 'hashtag'
      });
    }
  });
  
  // Sort by confidence and return top suggestions
  return suggestions
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, 10);
}

/**
 * Format hashtags for display
 */
export function formatHashtagsForDisplay(hashtags: string[]): string {
  return hashtags.map(tag => `#${tag}`).join(' ');
}

/**
 * Parse hashtags from multiple text fields
 */
export function extractHashtagsFromFields(fields: Record<string, string>): string[] {
  const allHashtags: string[] = [];
  
  Object.values(fields).forEach(text => {
    if (text) {
      allHashtags.push(...extractHashtags(text));
    }
  });
  
  return [...new Set(allHashtags)];
}

/**
 * Check if text contains any hashtags
 */
export function hasHashtags(text: string): boolean {
  return /#([a-zA-Z0-9_-]+)/.test(text);
}

/**
 * Count hashtags in text
 */
export function countHashtags(text: string): number {
  return extractHashtags(text).length;
}

/**
 * Replace hashtags in text with clickable links (for display)
 */
export function makeHashtagsClickable(
  text: string, 
  onHashtagClick: (hashtag: string) => void
): string {
  return text.replace(
    /#([a-zA-Z0-9_-]+)/g, 
    '<span class="hashtag-link cursor-pointer text-blue-500 hover:text-blue-700" data-hashtag="$1">#$1</span>'
  );
}

/**
 * Get hashtag color based on tag name (for consistent UI)
 */
export function getHashtagColor(hashtag: string): string {
  const colors = [
    '#3B82F6', // blue
    '#10B981', // green
    '#F59E0B', // yellow
    '#EF4444', // red
    '#8B5CF6', // purple
    '#F97316', // orange
    '#06B6D4', // cyan
    '#EC4899', // pink
  ];
  
  // Use hashtag string to consistently assign colors
  const hash = hashtag.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
}
