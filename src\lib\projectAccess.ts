import { supabase, Project, Profile } from './supabase';
import { toast } from 'sonner';

export interface ProjectAccess {
  id: string;
  project_id: string;
  user_id: string;
  granted_by: string;
  access_type: 'view' | 'edit' | 'admin';
  expires_at: string | null;
  created_at: string;
  project?: Project;
  user?: Profile;
  granted_by_user?: Profile;
}

export interface GrantAccessData {
  projectId: string;
  userId: string;
  accessType?: 'view' | 'edit' | 'admin';
  expiresAt?: string;
}

export interface ProjectAccessFilters {
  projectId?: string;
  userId?: string;
  accessType?: string;
  includeExpired?: boolean;
  search?: string;
  limit?: number;
  offset?: number;
}

export class ProjectAccessService {
  /**
   * Grant access to a project (admin only)
   */
  static async grantProjectAccess(data: GrantAccessData): Promise<{ access: ProjectAccess | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { access: null, error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { access: null, error: new Error('Admin access required') };
      }

      // Check if project exists and belongs to admin or is public
      const { data: project } = await supabase
        .from('projects')
        .select('*')
        .eq('id', data.projectId)
        .single();

      if (!project) {
        return { access: null, error: new Error('Project not found') };
      }

      // Check if target user exists
      const { data: targetUser } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', data.userId)
        .single();

      if (!targetUser) {
        return { access: null, error: new Error('Target user not found') };
      }

      // Insert or update project access
      const accessData = {
        project_id: data.projectId,
        user_id: data.userId,
        granted_by: user.id,
        access_type: data.accessType || 'view',
        expires_at: data.expiresAt || null,
      };

      const { data: access, error } = await supabase
        .from('project_access')
        .upsert(accessData, { onConflict: 'project_id,user_id' })
        .select(`
          *,
          project:projects(*),
          user:profiles!project_access_user_id_fkey(*),
          granted_by_user:profiles!project_access_granted_by_fkey(*)
        `)
        .single();

      if (error) {
        console.error('Error granting project access:', error);
        return { access: null, error: new Error(error.message) };
      }

      return { access: access as ProjectAccess, error: null };
    } catch (error) {
      console.error('Error granting project access:', error);
      return { access: null, error: error as Error };
    }
  }

  /**
   * Revoke access to a project (admin only)
   */
  static async revokeProjectAccess(projectId: string, userId: string): Promise<{ success: boolean; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { success: false, error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { success: false, error: new Error('Admin access required') };
      }

      const { error } = await supabase
        .from('project_access')
        .delete()
        .eq('project_id', projectId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error revoking project access:', error);
        return { success: false, error: new Error(error.message) };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Error revoking project access:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * Check if user has access to a project
   */
  static async hasProjectAccess(projectId: string, userId?: string): Promise<{ hasAccess: boolean; accessType?: string; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;
      
      if (!targetUserId) {
        return { hasAccess: false, error: new Error('User not authenticated') };
      }

      // Check if user owns the project
      const { data: project } = await supabase
        .from('projects')
        .select('user_id, is_public')
        .eq('id', projectId)
        .single();

      if (!project) {
        return { hasAccess: false, error: new Error('Project not found') };
      }

      // Owner always has access
      if (project.user_id === targetUserId) {
        return { hasAccess: true, accessType: 'admin', error: null };
      }

      // Check if project is public
      if (project.is_public) {
        return { hasAccess: true, accessType: 'view', error: null };
      }

      // Check explicit access grants
      const { data: access } = await supabase
        .from('project_access')
        .select('access_type, expires_at')
        .eq('project_id', projectId)
        .eq('user_id', targetUserId)
        .single();

      if (!access) {
        return { hasAccess: false, error: null };
      }

      // Check if access has expired
      if (access.expires_at && new Date(access.expires_at) < new Date()) {
        return { hasAccess: false, error: null };
      }

      return { hasAccess: true, accessType: access.access_type, error: null };
    } catch (error) {
      console.error('Error checking project access:', error);
      return { hasAccess: false, error: error as Error };
    }
  }

  /**
   * Get user's accessible projects
   */
  static async getUserAccessibleProjects(filters: ProjectAccessFilters = {}): Promise<{ projects: Project[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { projects: [], error: new Error('User not authenticated') };
      }

      // Get projects user has explicit access to
      let accessQuery = supabase
        .from('project_access')
        .select(`
          project:projects(*)
        `)
        .eq('user_id', user.id);

      // Filter out expired access unless explicitly requested
      if (!filters.includeExpired) {
        accessQuery = accessQuery.or('expires_at.is.null,expires_at.gt.now()');
      }

      const { data: accessData } = await accessQuery;

      // Get user's own projects
      let ownProjectsQuery = supabase
        .from('projects')
        .select('*')
        .eq('user_id', user.id);

      // Get public projects
      let publicProjectsQuery = supabase
        .from('projects')
        .select('*')
        .eq('is_public', true);

      // Apply search filter if provided
      if (filters.search) {
        const searchFilter = `title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`;
        ownProjectsQuery = ownProjectsQuery.or(searchFilter);
        publicProjectsQuery = publicProjectsQuery.or(searchFilter);
      }

      const [ownProjectsResult, publicProjectsResult] = await Promise.all([
        ownProjectsQuery,
        publicProjectsQuery
      ]);

      // Combine all projects and remove duplicates
      const allProjects: Project[] = [];
      const projectIds = new Set<string>();

      // Add own projects
      if (ownProjectsResult.data) {
        ownProjectsResult.data.forEach(project => {
          if (!projectIds.has(project.id)) {
            allProjects.push(project);
            projectIds.add(project.id);
          }
        });
      }

      // Add accessible projects
      if (accessData) {
        accessData.forEach(item => {
          if (item.project && !projectIds.has(item.project.id)) {
            allProjects.push(item.project);
            projectIds.add(item.project.id);
          }
        });
      }

      // Add public projects
      if (publicProjectsResult.data) {
        publicProjectsResult.data.forEach(project => {
          if (!projectIds.has(project.id)) {
            allProjects.push(project);
            projectIds.add(project.id);
          }
        });
      }

      // Apply additional filters
      let filteredProjects = allProjects;

      // Apply limit and offset
      if (filters.offset) {
        filteredProjects = filteredProjects.slice(filters.offset);
      }
      if (filters.limit) {
        filteredProjects = filteredProjects.slice(0, filters.limit);
      }

      return { projects: filteredProjects, error: null };
    } catch (error) {
      console.error('Error getting user accessible projects:', error);
      return { projects: [], error: error as Error };
    }
  }

  /**
   * Get project access list (admin only)
   */
  static async getProjectAccessList(filters: ProjectAccessFilters = {}): Promise<{ accessList: ProjectAccess[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { accessList: [], error: new Error('User not authenticated') };
      }

      // Check if user is admin
      const { data: profile } = await supabase
        .from('profiles')
        .select('subscription_status')
        .eq('id', user.id)
        .single();

      if (profile?.subscription_status !== 'admin') {
        return { accessList: [], error: new Error('Admin access required') };
      }

      let query = supabase
        .from('project_access')
        .select(`
          *,
          project:projects(*),
          user:profiles!project_access_user_id_fkey(*),
          granted_by_user:profiles!project_access_granted_by_fkey(*)
        `);

      // Apply filters
      if (filters.projectId) {
        query = query.eq('project_id', filters.projectId);
      }

      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }

      if (filters.accessType) {
        query = query.eq('access_type', filters.accessType);
      }

      // Filter out expired access unless explicitly requested
      if (!filters.includeExpired) {
        query = query.or('expires_at.is.null,expires_at.gt.now()');
      }

      // Apply search - we'll handle this differently since we can't easily search across joined tables
      // For now, we'll load all data and filter client-side if search is needed

      // Apply pagination
      if (filters.offset) {
        query = query.range(filters.offset, (filters.offset + (filters.limit || 50)) - 1);
      } else if (filters.limit) {
        query = query.limit(filters.limit);
      }

      query = query.order('created_at', { ascending: false });

      const { data: accessList, error } = await query;

      if (error) {
        console.error('Error getting project access list:', error);
        return { accessList: [], error: new Error(error.message) };
      }

      return { accessList: accessList as ProjectAccess[] || [], error: null };
    } catch (error) {
      console.error('Error getting project access list:', error);
      return { accessList: [], error: error as Error };
    }
  }
}
