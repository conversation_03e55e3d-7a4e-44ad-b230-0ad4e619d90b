import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { Check, ChevronDown, ChevronUp } from "lucide-react"
import { cn } from "@/lib/utils"

const Select = SelectPrimitive.Root
const SelectGroup = SelectPrimitive.Group
const SelectValue = SelectPrimitive.Value
const SelectTrigger = SelectPrimitive.Trigger
const SelectContent = SelectPrimitive.Content
const SelectLabel = SelectPrimitive.Label
const SelectItem = SelectPrimitive.Item
const SelectSeparator = SelectPrimitive.Separator
const SelectScrollUpButton = SelectPrimitive.ScrollUpButton
const SelectScrollDownButton = SelectPrimitive.ScrollDownButton
const SelectViewport = SelectPrimitive.Viewport
const SelectItemIndicator = SelectPrimitive.ItemIndicator

export {
  Select,
  SelectGroup,
  SelectValue,
  SelectTrigger,
  SelectContent,
  SelectLabel,
  SelectItem,
  SelectSeparator,
  SelectScrollUpButton,
  SelectScrollDownButton,
  SelectViewport,
  SelectItemIndicator,
}
