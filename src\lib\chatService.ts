import { supabase, Project, Chat, Message, ChatInsert, MessageInsert, ProjectInsert } from './supabase';
import { generatePrompt } from '@/utils/generatePrompt';

export interface CreateProjectData {
  name: string;
  description?: string;
  customInstructions?: string;
  isPublic?: boolean;
}

export interface CreateChatData {
  title?: string;
  projectId?: string;
}

export interface SendMessageData {
  chatId: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  metadata?: Record<string, unknown>;
}

export class ChatService {
  /**
   * Create a new project
   */
  static async createProject(data: CreateProjectData): Promise<{ project: Project | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { project: null, error: new Error('User not authenticated') };
      }

      const projectData: ProjectInsert = {
        user_id: user.id,
        name: data.name,
        description: data.description || null,
        custom_instructions: data.customInstructions || null,
        is_public: data.isPublic || false
      };

      const { data: project, error } = await supabase
        .from('projects')
        .insert(projectData)
        .select()
        .single();

      if (error) {
        console.error('Error creating project:', error);
        return { project: null, error: new Error(error.message) };
      }

      return { project, error: null };
    } catch (error) {
      console.error('Error creating project:', error);
      return { project: null, error: error as Error };
    }
  }

  /**
   * Get user's projects with their chats
   */
  static async getUserProjects(): Promise<{ projects: Project[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { projects: [], error: new Error('User not authenticated') };
      }

      const { data: projects, error } = await supabase
        .from('projects')
        .select(`
          *,
          chats (
            id,
            title,
            last_message_preview,
            created_at,
            updated_at
          )
        `)
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching projects:', error);
        return { projects: [], error: new Error(error.message) };
      }

      return { projects: projects || [], error: null };
    } catch (error) {
      console.error('Error fetching projects:', error);
      return { projects: [], error: error as Error };
    }
  }

  /**
   * Get standalone chats (not in any project)
   */
  static async getStandaloneChats(): Promise<{ chats: Chat[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { chats: [], error: new Error('User not authenticated') };
      }

      const { data: chats, error } = await supabase
        .from('chats')
        .select('*')
        .eq('user_id', user.id)
        .is('project_id', null)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching standalone chats:', error);
        return { chats: [], error: new Error(error.message) };
      }

      return { chats: chats || [], error: null };
    } catch (error) {
      console.error('Error fetching standalone chats:', error);
      return { chats: [], error: error as Error };
    }
  }

  /**
   * Create a new chat
   */
  static async createChat(data: CreateChatData): Promise<{ chat: Chat | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return { chat: null, error: new Error('User not authenticated') };
      }

      const chatData: ChatInsert = {
        user_id: user.id,
        project_id: data.projectId || null,
        title: data.title || 'New Chat',
        last_message_preview: null
      };

      const { data: chat, error } = await supabase
        .from('chats')
        .insert(chatData)
        .select()
        .single();

      if (error) {
        console.error('Error creating chat:', error);
        return { chat: null, error: new Error(error.message) };
      }

      return { chat, error: null };
    } catch (error) {
      console.error('Error creating chat:', error);
      return { chat: null, error: error as Error };
    }
  }

  /**
   * Get a chat with its messages
   */
  static async getChat(chatId: string): Promise<{ chat: Chat | null; messages: Message[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { chat: null, messages: [], error: new Error('User not authenticated') };
      }

      // Get chat
      const { data: chat, error: chatError } = await supabase
        .from('chats')
        .select('*')
        .eq('id', chatId)
        .eq('user_id', user.id)
        .single();

      if (chatError) {
        console.error('Error fetching chat:', chatError);
        return { chat: null, messages: [], error: new Error(chatError.message) };
      }

      // Get messages
      const { data: messages, error: messagesError } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('chat_id', chatId)
        .order('created_at', { ascending: true });

      if (messagesError) {
        console.error('Error fetching messages:', messagesError);
        return { chat, messages: [], error: new Error(messagesError.message) };
      }

      return { chat, messages: messages || [], error: null };
    } catch (error) {
      console.error('Error fetching chat:', error);
      return { chat: null, messages: [], error: error as Error };
    }
  }

  /**
   * Send a message to a chat
   */
  static async sendMessage(data: SendMessageData): Promise<{ message: Message | null; error: Error | null }> {
    try {
      const messageData: MessageInsert = {
        chat_id: data.chatId,
        role: data.role,
        content: data.content,
        metadata: data.metadata || {}
      };

      const { data: message, error } = await supabase
        .from('chat_messages')
        .insert(messageData)
        .select()
        .single();

      if (error) {
        console.error('Error sending message:', error);
        return { message: null, error: new Error(error.message) };
      }

      return { message, error: null };
    } catch (error) {
      console.error('Error sending message:', error);
      return { message: null, error: error as Error };
    }
  }

  /**
   * Generate AI response for a chat
   */
  static async generateResponse(chatId: string, userMessage: string, promptStyle: string = 'technical'): Promise<{ message: Message | null; error: Error | null }> {
    try {
      // Get chat and project context
      const { chat, messages, error: chatError } = await this.getChat(chatId);
      
      if (chatError || !chat) {
        return { message: null, error: chatError || new Error('Chat not found') };
      }

      // Get project context if chat is in a project
      let projectContext = '';
      if (chat.project_id) {
        const { data: project } = await supabase
          .from('projects')
          .select('custom_instructions')
          .eq('id', chat.project_id)
          .single();
        
        if (project?.custom_instructions) {
          projectContext = `Project Instructions: ${project.custom_instructions}\n\n`;
        }
      }

      // Build conversation context
      const conversationContext = messages
        .slice(-10) // Last 10 messages for context
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      // Generate response using the existing prompt generation system
      const fullPrompt = `${projectContext}${conversationContext}\nuser: ${userMessage}`;
      const aiResponse = await generatePrompt(fullPrompt, promptStyle);

      if (!aiResponse) {
        return { message: null, error: new Error('Failed to generate AI response') };
      }

      // Save AI response as message
      const { message, error } = await this.sendMessage({
        chatId,
        content: aiResponse,
        role: 'assistant',
        metadata: {
          promptStyle,
          hasProjectContext: !!chat.project_id
        }
      });

      return { message, error };
    } catch (error) {
      console.error('Error generating response:', error);
      return { message: null, error: error as Error };
    }
  }

  /**
   * Update chat title
   */
  static async updateChatTitle(chatId: string, title: string): Promise<{ error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { error: new Error('User not authenticated') };
      }

      const { error } = await supabase
        .from('chats')
        .update({ title })
        .eq('id', chatId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating chat title:', error);
        return { error: new Error(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Error updating chat title:', error);
      return { error: error as Error };
    }
  }

  /**
   * Delete a chat
   */
  static async deleteChat(chatId: string): Promise<{ error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { error: new Error('User not authenticated') };
      }

      const { error } = await supabase
        .from('chats')
        .delete()
        .eq('id', chatId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting chat:', error);
        return { error: new Error(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Error deleting chat:', error);
      return { error: error as Error };
    }
  }

  /**
   * Delete a project and all its chats
   */
  static async deleteProject(projectId: string): Promise<{ error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { error: new Error('User not authenticated') };
      }

      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error deleting project:', error);
        return { error: new Error(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Error deleting project:', error);
      return { error: error as Error };
    }
  }
}
