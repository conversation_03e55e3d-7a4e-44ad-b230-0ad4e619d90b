import { loadStripe, Stripe } from '@stripe/stripe-js';
import { supabase, SubscriptionPlan, UserSubscription, PaymentHistory } from './supabase';

// Initialize Stripe
let stripePromise: Promise<Stripe | null>;

const getStripe = () => {
  if (!stripePromise) {
    const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
    if (!publishableKey) {
      console.error('Stripe publishable key not found');
      return null;
    }
    stripePromise = loadStripe(publishableKey);
  }
  return stripePromise;
};

export interface CreateCheckoutSessionData {
  planId: string;
  billingCycle: 'monthly' | 'yearly';
  successUrl?: string;
  cancelUrl?: string;
}

export interface SubscriptionWithPlan extends UserSubscription {
  plan: SubscriptionPlan;
}

export class StripeService {
  /**
   * Create a Stripe checkout session for subscription
   */
  static async createCheckoutSession(data: CreateCheckoutSessionData): Promise<{ url: string | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { url: null, error: new Error('User not authenticated') };
      }

      // Get the subscription plan
      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('id', data.planId)
        .single();

      if (planError || !plan) {
        return { url: null, error: new Error('Subscription plan not found') };
      }

      const priceId = data.billingCycle === 'yearly' 
        ? plan.stripe_price_id_yearly 
        : plan.stripe_price_id_monthly;

      if (!priceId) {
        return { url: null, error: new Error('Price ID not configured for this plan') };
      }

      // Call Supabase Edge Function to create checkout session
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(`${supabaseUrl}/functions/v1/create-checkout-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
        },
        body: JSON.stringify({
          priceId,
          planId: data.planId,
          successUrl: data.successUrl || `${window.location.origin}/dashboard?success=true`,
          cancelUrl: data.cancelUrl || `${window.location.origin}/pricing?canceled=true`,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        return { url: null, error: new Error(result.error || 'Failed to create checkout session') };
      }

      return { url: result.url, error: null };
    } catch (error) {
      console.error('Error creating checkout session:', error);
      return { url: null, error: error as Error };
    }
  }

  /**
   * Get user's current subscription
   */
  static async getUserSubscription(): Promise<{ subscription: SubscriptionWithPlan | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { subscription: null, error: new Error('User not authenticated') };
      }

      const { data: subscription, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          plan:subscription_plans(*)
        `)
        .eq('user_id', user.id)
        .eq('status', 'active')
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        return { subscription: null, error: new Error(error.message) };
      }

      return { subscription: subscription as SubscriptionWithPlan, error: null };
    } catch (error) {
      console.error('Error fetching user subscription:', error);
      return { subscription: null, error: error as Error };
    }
  }

  /**
   * Get all available subscription plans
   */
  static async getSubscriptionPlans(): Promise<{ plans: SubscriptionPlan[]; error: Error | null }> {
    try {
      const { data: plans, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('price_monthly', { ascending: true });

      if (error) {
        return { plans: [], error: new Error(error.message) };
      }

      return { plans: plans || [], error: null };
    } catch (error) {
      console.error('Error fetching subscription plans:', error);
      return { plans: [], error: error as Error };
    }
  }

  /**
   * Cancel user's subscription
   */
  static async cancelSubscription(): Promise<{ success: boolean; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { success: false, error: new Error('User not authenticated') };
      }

      // Call Supabase Edge Function to cancel subscription
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(`${supabaseUrl}/functions/v1/cancel-subscription`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return { success: false, error: new Error(result.error || 'Failed to cancel subscription') };
      }

      return { success: true, error: null };
    } catch (error) {
      console.error('Error canceling subscription:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * Get user's payment history
   */
  static async getPaymentHistory(): Promise<{ payments: PaymentHistory[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { payments: [], error: new Error('User not authenticated') };
      }

      const { data: payments, error } = await supabase
        .from('payment_history')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        return { payments: [], error: new Error(error.message) };
      }

      return { payments: payments || [], error: null };
    } catch (error) {
      console.error('Error fetching payment history:', error);
      return { payments: [], error: error as Error };
    }
  }

  /**
   * Create customer portal session
   */
  static async createPortalSession(): Promise<{ url: string | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { url: null, error: new Error('User not authenticated') };
      }

      // Call Supabase Edge Function to create portal session
      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      const response = await fetch(`${supabaseUrl}/functions/v1/create-portal-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`,
        },
        body: JSON.stringify({
          returnUrl: `${window.location.origin}/dashboard`,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        return { url: null, error: new Error(result.error || 'Failed to create portal session') };
      }

      return { url: result.url, error: null };
    } catch (error) {
      console.error('Error creating portal session:', error);
      return { url: null, error: error as Error };
    }
  }
}

export { getStripe };
