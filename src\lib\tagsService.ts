import { supabase } from './supabase';
import { extractHashtags, normalizeHashtag } from '@/utils/hashtagUtils';

export interface Tag {
  id: string;
  name: string;
  color: string;
  description?: string;
  usage_count: number;
  is_system_tag: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  color: string;
  icon?: string;
  usage_count: number;
  is_system_category: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface TagFilters {
  search?: string;
  isSystemTag?: boolean;
  sortBy?: 'name' | 'usage_count' | 'created_at';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
}

export interface CategoryFilters {
  search?: string;
  isSystemCategory?: boolean;
  sortBy?: 'name' | 'usage_count' | 'created_at';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
}

export class TagsService {
  /**
   * Get all available tags
   */
  static async getTags(filters: TagFilters = {}): Promise<{ tags: Tag[]; error: Error | null }> {
    try {
      let query = supabase
        .from('tags')
        .select('*');

      // Apply filters
      if (filters.search) {
        query = query.ilike('name', `%${filters.search}%`);
      }

      if (filters.isSystemTag !== undefined) {
        query = query.eq('is_system_tag', filters.isSystemTag);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'usage_count';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply limit
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      const { data: tags, error } = await query;

      if (error) {
        console.error('Error fetching tags:', error);
        return { tags: [], error: new Error(error.message) };
      }

      return { tags: tags || [], error: null };
    } catch (error) {
      console.error('Error fetching tags:', error);
      return { tags: [], error: error as Error };
    }
  }

  /**
   * Get all available categories
   */
  static async getCategories(filters: CategoryFilters = {}): Promise<{ categories: Category[]; error: Error | null }> {
    try {
      let query = supabase
        .from('categories')
        .select('*');

      // Apply filters
      if (filters.search) {
        query = query.ilike('name', `%${filters.search}%`);
      }

      if (filters.isSystemCategory !== undefined) {
        query = query.eq('is_system_category', filters.isSystemCategory);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'usage_count';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply limit
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      const { data: categories, error } = await query;

      if (error) {
        console.error('Error fetching categories:', error);
        return { categories: [], error: new Error(error.message) };
      }

      return { categories: categories || [], error: null };
    } catch (error) {
      console.error('Error fetching categories:', error);
      return { categories: [], error: error as Error };
    }
  }

  /**
   * Create a new tag
   */
  static async createTag(name: string, color?: string, description?: string): Promise<{ tag: Tag | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { tag: null, error: new Error('User not authenticated') };
      }

      const normalizedName = normalizeHashtag(name);
      if (!normalizedName) {
        return { tag: null, error: new Error('Invalid tag name format') };
      }

      const { data: tag, error } = await supabase
        .from('tags')
        .insert({
          name: normalizedName,
          color: color || '#3B82F6',
          description,
          created_by: user.id,
          is_system_tag: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating tag:', error);
        return { tag: null, error: new Error(error.message) };
      }

      return { tag, error: null };
    } catch (error) {
      console.error('Error creating tag:', error);
      return { tag: null, error: error as Error };
    }
  }

  /**
   * Create a new category
   */
  static async createCategory(
    name: string, 
    color?: string, 
    description?: string, 
    icon?: string
  ): Promise<{ category: Category | null; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { category: null, error: new Error('User not authenticated') };
      }

      const { data: category, error } = await supabase
        .from('categories')
        .insert({
          name,
          color: color || '#10B981',
          description,
          icon,
          created_by: user.id,
          is_system_category: false
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating category:', error);
        return { category: null, error: new Error(error.message) };
      }

      return { category, error: null };
    } catch (error) {
      console.error('Error creating category:', error);
      return { category: null, error: error as Error };
    }
  }

  /**
   * Get or create tags from hashtag names
   */
  static async getOrCreateTags(tagNames: string[]): Promise<{ tags: Tag[]; error: Error | null }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return { tags: [], error: new Error('User not authenticated') };
      }

      const normalizedNames = tagNames
        .map(name => normalizeHashtag(name))
        .filter(Boolean) as string[];

      if (normalizedNames.length === 0) {
        return { tags: [], error: null };
      }

      // First, try to get existing tags
      const { data: existingTags, error: fetchError } = await supabase
        .from('tags')
        .select('*')
        .in('name', normalizedNames);

      if (fetchError) {
        console.error('Error fetching existing tags:', fetchError);
        return { tags: [], error: new Error(fetchError.message) };
      }

      const existingTagNames = existingTags?.map(tag => tag.name) || [];
      const newTagNames = normalizedNames.filter(name => !existingTagNames.includes(name));

      // Create new tags if needed
      let newTags: Tag[] = [];
      if (newTagNames.length > 0) {
        const { data: createdTags, error: createError } = await supabase
          .from('tags')
          .insert(
            newTagNames.map(name => ({
              name,
              created_by: user.id,
              is_system_tag: false,
              color: '#3B82F6'
            }))
          )
          .select();

        if (createError) {
          console.error('Error creating new tags:', createError);
          return { tags: existingTags || [], error: new Error(createError.message) };
        }

        newTags = createdTags || [];
      }

      const allTags = [...(existingTags || []), ...newTags];
      return { tags: allTags, error: null };
    } catch (error) {
      console.error('Error getting or creating tags:', error);
      return { tags: [], error: error as Error };
    }
  }

  /**
   * Add tags to a project
   */
  static async addTagsToProject(projectId: string, tagIds: string[]): Promise<{ error: Error | null }> {
    try {
      if (tagIds.length === 0) return { error: null };

      const { error } = await supabase
        .from('project_tags')
        .insert(
          tagIds.map(tagId => ({
            project_id: projectId,
            tag_id: tagId
          }))
        );

      if (error) {
        console.error('Error adding tags to project:', error);
        return { error: new Error(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Error adding tags to project:', error);
      return { error: error as Error };
    }
  }

  /**
   * Remove tags from a project
   */
  static async removeTagsFromProject(projectId: string, tagIds?: string[]): Promise<{ error: Error | null }> {
    try {
      let query = supabase
        .from('project_tags')
        .delete()
        .eq('project_id', projectId);

      if (tagIds && tagIds.length > 0) {
        query = query.in('tag_id', tagIds);
      }

      const { error } = await query;

      if (error) {
        console.error('Error removing tags from project:', error);
        return { error: new Error(error.message) };
      }

      return { error: null };
    } catch (error) {
      console.error('Error removing tags from project:', error);
      return { error: error as Error };
    }
  }

  /**
   * Get tags for a project
   */
  static async getProjectTags(projectId: string): Promise<{ tags: Tag[]; error: Error | null }> {
    try {
      const { data, error } = await supabase
        .from('project_tags')
        .select(`
          tags (*)
        `)
        .eq('project_id', projectId);

      if (error) {
        console.error('Error fetching project tags:', error);
        return { tags: [], error: new Error(error.message) };
      }

      const tags = data?.map(item => (item as any).tags).filter(Boolean) || [];
      return { tags, error: null };
    } catch (error) {
      console.error('Error fetching project tags:', error);
      return { tags: [], error: error as Error };
    }
  }

  /**
   * Process hashtags from text and create/link tags to project
   */
  static async processHashtagsForProject(
    projectId: string, 
    text: string
  ): Promise<{ tags: Tag[]; error: Error | null }> {
    try {
      const hashtags = extractHashtags(text);
      if (hashtags.length === 0) {
        return { tags: [], error: null };
      }

      // Get or create tags
      const { tags, error: tagsError } = await this.getOrCreateTags(hashtags);
      if (tagsError) {
        return { tags: [], error: tagsError };
      }

      // Add tags to project
      const tagIds = tags.map(tag => tag.id);
      const { error: linkError } = await this.addTagsToProject(projectId, tagIds);
      if (linkError) {
        return { tags, error: linkError };
      }

      return { tags, error: null };
    } catch (error) {
      console.error('Error processing hashtags for project:', error);
      return { tags: [], error: error as Error };
    }
  }
}
